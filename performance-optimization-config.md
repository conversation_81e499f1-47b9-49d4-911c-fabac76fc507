# 核心产品定义文件同步任务性能优化配置指南

## 1. 数据库连接池优化配置

### 1.1 HikariCP连接池配置（推荐）
在 `application.yml` 或 `application.properties` 中添加以下配置：

```yaml
spring:
  datasource:
    hikari:
      # 连接池最大连接数 - 根据并发分片数量调整
      maximum-pool-size: 50
      # 最小空闲连接数
      minimum-idle: 20
      # 连接超时时间（毫秒）
      connection-timeout: 60000
      # 空闲连接超时时间（毫秒）
      idle-timeout: 300000
      # 连接最大生命周期（毫秒）
      max-lifetime: 1800000
      # 连接泄漏检测阈值（毫秒）
      leak-detection-threshold: 60000
      # 连接池名称
      pool-name: LimitBatchHikariCP
      # 连接测试查询
      connection-test-query: SELECT 1 FROM DUAL
```

### 1.2 Druid连接池配置（备选）
```yaml
spring:
  datasource:
    druid:
      # 初始化连接数
      initial-size: 10
      # 最小连接数
      min-idle: 20
      # 最大连接数
      max-active: 50
      # 获取连接等待超时时间
      max-wait: 60000
      # 间隔多久进行一次检测，检测需要关闭的空闲连接
      time-between-eviction-runs-millis: 60000
      # 连接保持空闲而不被驱逐的最小时间
      min-evictable-idle-time-millis: 300000
      # 验证连接有效性的SQL
      validation-query: SELECT 1 FROM DUAL
      # 申请连接时执行validationQuery检测连接是否有效
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效
      test-on-return: false
      # 申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
      test-while-idle: true
```

## 2. MyBatis批量处理优化配置

### 2.1 MyBatis核心配置
```yaml
mybatis:
  configuration:
    # 启用批量执行器，提升批量操作性能
    default-executor-type: BATCH
    # 本地缓存作用域设置为STATEMENT，避免缓存问题
    local-cache-scope: STATEMENT
    # 启用延迟加载
    lazy-loading-enabled: true
    # 积极延迟加载
    aggressive-lazy-loading: false
    # 允许多结果集
    multiple-result-sets-enabled: true
    # 使用列标签代替列名
    use-column-label: true
    # 允许JDBC生成主键
    use-generated-keys: false
    # 自动映射策略
    auto-mapping-behavior: PARTIAL
    # 自动映射未知列行为
    auto-mapping-unknown-column-behavior: WARNING
    # 缓存启用
    cache-enabled: true
    # 调用setter方法时，是否调用getter方法
    call-setters-on-nulls: false
    # 日志实现
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
```

### 2.2 分页插件配置（如果使用PageHelper）
```yaml
pagehelper:
  # 数据库方言
  helper-dialect: oracle
  # 分页合理化参数
  reasonable: true
  # 支持通过Mapper接口参数来传递分页参数
  support-methods-arguments: true
  # 分页参数
  params: count=countSql
```

## 3. JVM优化配置

### 3.1 JVM内存配置
```bash
# 堆内存设置
-Xms4g -Xmx8g
# 新生代内存设置
-Xmn2g
# 元空间设置
-XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m
# 直接内存设置
-XX:MaxDirectMemorySize=1g
```

### 3.2 垃圾回收器配置
```bash
# 使用G1垃圾回收器（推荐）
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:G1NewSizePercent=30
-XX:G1MaxNewSizePercent=40
-XX:G1MixedGCLiveThresholdPercent=85
-XX:G1MixedGCCountTarget=8
-XX:G1OldCSetRegionThreshold=10
```

## 4. 线程池配置

### 4.1 异步任务线程池配置
```yaml
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 10
        # 最大线程数
        max-size: 50
        # 队列容量
        queue-capacity: 1000
        # 线程名前缀
        thread-name-prefix: limit-batch-
        # 线程空闲时间
        keep-alive: 60s
      shutdown:
        # 等待任务完成
        await-termination: true
        # 等待时间
        await-termination-period: 60s
```

## 5. 文件处理优化配置

### 5.1 文件读取缓冲区配置
```yaml
# 自定义配置
limit-batch:
  file:
    # 文件读取缓冲区大小（字节）
    buffer-size: 65536
    # 文件编码
    charset: UTF-8
    # 并行处理线程数
    parallel-threads: 8
```

## 6. 监控和日志配置

### 6.1 性能监控配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 6.2 日志配置优化
```yaml
logging:
  level:
    # 批处理相关日志级别
    com.hsjry.core.limit.batch: INFO
    # MyBatis SQL日志（生产环境建议设置为WARN）
    com.hsjry.core.limit.batch.dal.dao.mapper: WARN
    # HikariCP连接池日志
    com.zaxxer.hikari: WARN
  pattern:
    # 日志格式优化
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
```

## 7. 应用配置建议

### 7.1 启动参数配置
```bash
# 完整的JVM启动参数示例
java -server \
  -Xms4g -Xmx8g -Xmn2g \
  -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
  -XX:MaxDirectMemorySize=1g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:G1HeapRegionSize=16m \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -XX:+PrintGCApplicationStoppedTime \
  -Xloggc:gc.log \
  -XX:+UseGCLogFileRotation \
  -XX:NumberOfGCLogFiles=5 \
  -XX:GCLogFileSize=100M \
  -jar limit-batch-application.jar
```

## 8. 配置验证和调优建议

### 8.1 性能测试验证
1. 使用小数据集（1万条）验证配置正确性
2. 逐步增加数据量测试（10万、100万、1000万）
3. 监控关键指标：
   - 数据库连接池使用率
   - JVM内存使用情况
   - GC频率和停顿时间
   - 批处理执行时间

### 8.2 调优步骤
1. 基线测试：记录优化前的性能数据
2. 逐项配置：按优先级逐步应用配置
3. 性能验证：每次配置后进行性能测试
4. 参数调整：根据测试结果微调参数
5. 生产验证：在生产环境进行最终验证

## 9. 预期性能提升

基于以上配置优化，预期性能提升：

| 配置项目 | 优化前 | 优化后 | 提升效果 |
|---------|--------|--------|----------|
| 数据库连接 | 默认10个 | 50个 | 5倍并发能力 |
| 批量插入 | INSERT ALL | INSERT VALUES | 20-30%性能提升 |
| JVM内存 | 默认配置 | 8G堆内存 | 减少GC影响 |
| 线程池 | 默认配置 | 50个线程 | 提升并行处理能力 |

**总体预期：1000万条记录处理时间从2-3小时优化到10分钟内**
