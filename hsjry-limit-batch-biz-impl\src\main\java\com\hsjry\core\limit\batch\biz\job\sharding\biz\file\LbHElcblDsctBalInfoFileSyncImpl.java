/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.convert.file.LbHElcblDsctBalInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHElcblDsctBalInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblDsctBalInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-贴现余额信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/7/17
 */
@Slf4j
@Service("lbHElcblDsctBalInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbHElcblDsctBalInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHElcblDsctBalInfoData> {

    /** 法人行机构编号列数 */
    private static final int ORG_NO_NUM = 1;
    /** 贴现编号列数 */
    private static final int DIC_CNO_NUM = 2;
    /** 票据编号列数 */
    private static final int BILL_NO_NUM = 3;
    /** 子票区间起始列数 */
    private static final int BILL_RANGE_START_NUM = 4;
    /** 子票区间截止列数 */
    private static final int BILL_RANGE_END_NUM = 5;
    /** 贴现客户编号列数 */
    private static final int USER_ID_NUM = 6;
    /** 贴现客户名称列数 */
    private static final int USER_NAME_NUM = 7;
    /** 票据币种列数 */
    private static final int CURRENCY_NUM = 8;
    /** 贴现金额列数 */
    private static final int DISCOUNT_AMT_NUM = 9;
    /** 贴现余额列数 */
    private static final int DISCOUNT_BAL_NUM = 10;
    /** 贴现起始日期列数 */
    private static final int START_DATE_NUM = 11;
    /** 贴现到期日期列数 */
    private static final int END_DATE_NUM = 12;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 13;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 14;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 14;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHElcblDsctBalInfoDao lbHElcblDsctBalInfoDao;
    @Value("${project.elcbl.dsct.bal.info.filename:elcbl_dsct_bal_info_[DATE].csv}")
    private String fileName;
    @Value("${project.elcbl.dsct.bal.info.remoteFilePath:/nfsdata/file/midd/ELCBL/DSCT_BAL_INFO/data/[DATE]/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHElcblDsctBalInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHElcblDsctBalInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst());

            // 使用并行流处理数据，提升性能
            List<LbHElcblDsctBalInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHElcblDsctBalInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHElcblDsctBalInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "电票系统-贴现余额信息文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "电票系统-贴现余额信息文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        // 设置数据日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_s_elcbl_dsct_bal_info");
            lbHElcblDsctBalInfoDao.deleteByDataDate(dataDateStr);
        }


        // 使用并行流进行数据转换和验证
        List<LbHElcblDsctBalInfoDo> insertList = dataList.parallelStream().map(LbHElcblDsctBalInfoConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条电票系统-贴现余额信息数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_ELCBL_DSCT_BAL_INFO;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        Map<String, Object> jobParameters = jobInitDto.getJobParameters();
        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            "limit/elcbl/dsct_bal_info/" + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            File localFile = new File(filePath);
            //判断本地文件是否存在
            if (localFile.exists()) {
                log.info(prefixLog + "[{}]删除本地文件,localFilePath = [{}],fileName = [{}]", jobTradeDesc,
                    localFilePath, localFileName);
                localFile.delete();
            }
            if (!localFile.exists()) {
                String remoteFilePath = remoteFilePathDefine.replace(FileShardingUtils.FILE_DATE_CODE_MARK,
                    String.valueOf(acctDate));
                String remoteFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK,
                    String.valueOf(acctDate));
                download(remoteFilePath, remoteFileName, localFile, false);
                //文件分片
                log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
                List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                    skipFirst());
                log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                    jobShareds.size());
                sharedList.addAll(jobShareds);
            }
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHElcblDsctBalInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbHElcblDsctBalInfoData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 提取单行数据解析逻辑，提高代码复用性和可维护性
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHElcblDsctBalInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbHElcblDsctBalInfoData fileData = new LbHElcblDsctBalInfoData();
        // 设置字符串字段
        fileData.setOrgNo(split[ORG_NO_NUM - 1]);
        fileData.setDicCno(split[DIC_CNO_NUM - 1]);
        fileData.setBillNo(split[BILL_NO_NUM - 1]);
        fileData.setBillRangeStart(split[BILL_RANGE_START_NUM - 1]);
        fileData.setBillRangeEnd(split[BILL_RANGE_END_NUM - 1]);
        fileData.setUserId(split[USER_ID_NUM - 1]);
        fileData.setUserName(split[USER_NAME_NUM - 1]);
        fileData.setCurrency(split[CURRENCY_NUM - 1]);
        fileData.setStartDate(split[START_DATE_NUM - 1]);
        fileData.setEndDate(split[END_DATE_NUM - 1]);
        fileData.setCreateTime(split[CREATE_TIME_NUM - 1]);
        fileData.setUpdateTime(split[UPDATE_TIME_NUM - 1]);

        // 安全解析数字字段
        fileData.setDiscountAmt(parseBigDecimalSafely(split[DISCOUNT_AMT_NUM - 1], parseErrorCount));
        fileData.setDiscountBal(parseBigDecimalSafely(split[DISCOUNT_BAL_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 增强的数据验证方法
     * 使用Objects工具类和优化的验证逻辑
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbHElcblDsctBalInfoDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getUserId())) {
            log.warn("贴现客户编号为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getDicCno())) {
            log.warn("贴现编号为空,贴现客户编号:[{}]", data.getUserId());
            return false;
        }

        if (StringUtil.isBlank(data.getBillNo())) {
            log.warn("票据编号为空,贴现客户编号:[{}]", data.getUserId());
            return false;
        }

        // 额外的业务验证逻辑
        if (Objects.nonNull(data.getOrgNo()) && data.getOrgNo().length() > 32) {
            log.warn("法人行机构编号长度超限,贴现客户编号:[{}]", data.getUserId());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHElcblDsctBalInfoDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHElcblDsctBalInfoDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbHElcblDsctBalInfoDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }

}
