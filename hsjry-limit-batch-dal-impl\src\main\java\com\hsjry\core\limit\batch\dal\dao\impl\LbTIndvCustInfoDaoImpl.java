package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTIndvCustInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTIndvCustInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIndvCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIndvCustInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIndvCustInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTIndvCustInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-个人客户信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbTIndvCustInfoDaoImpl extends AbstractBaseDaoImpl<LbTIndvCustInfoDo, LbTIndvCustInfoMapper>
    implements LbTIndvCustInfoDao {
    /**
     * 分页查询
     *
     * @param lbTIndvCustInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTIndvCustInfoDo> selectPage(LbTIndvCustInfoQuery lbTIndvCustInfo, PageParam pageParam) {
        LbTIndvCustInfoExample example = buildExample(lbTIndvCustInfo);
        return PageHelper.<LbTIndvCustInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-个人客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public LbTIndvCustInfoDo selectByKey(String custNo) {
        LbTIndvCustInfoKeyDo lbTIndvCustInfoKeyDo = new LbTIndvCustInfoKeyDo();
        lbTIndvCustInfoKeyDo.setCustNo(custNo);
        lbTIndvCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTIndvCustInfoKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-个人客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public int deleteByKey(String custNo) {
        LbTIndvCustInfoKeyDo lbTIndvCustInfoKeyDo = new LbTIndvCustInfoKeyDo();
        lbTIndvCustInfoKeyDo.setCustNo(custNo);
        lbTIndvCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTIndvCustInfoKeyDo);
    }

    /**
     * 查询额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo 条件
     * @return List<LbTIndvCustInfoDo>
     */
    @Override
    public List<LbTIndvCustInfoDo> selectByExample(LbTIndvCustInfoQuery lbTIndvCustInfo) {
        return getMapper().selectByExample(buildExample(lbTIndvCustInfo));
    }

    /**
     * 新增额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTIndvCustInfoDo lbTIndvCustInfo) {
        if (lbTIndvCustInfo == null) {
            return -1;
        }

        lbTIndvCustInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTIndvCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTIndvCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTIndvCustInfo);
    }

    /**
     * 修改额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTIndvCustInfoDo lbTIndvCustInfo) {
        if (lbTIndvCustInfo == null) {
            return -1;
        }
        lbTIndvCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTIndvCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTIndvCustInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTIndvCustInfoDo lbTIndvCustInfo,
        LbTIndvCustInfoQuery lbTIndvCustInfoQuery) {
        lbTIndvCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTIndvCustInfo, buildExample(lbTIndvCustInfoQuery));
    }

    @Override
    public int truncateTable() {
        return getMapper().truncateTable();
    }

    @Override
    public int insertFromSource() {
        return getMapper().insertFromSource();
    }

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入个人客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    @Override
    public int insertFromSource(List<String> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return 0;
        }
        return getMapper().insertFromSourceByUserIdList(userIdList);
    }

    /**
     * 构建额度中心-中间表-个人客户信息Example信息
     *
     * @param lbTIndvCustInfo
     * @return
     */
    public LbTIndvCustInfoExample buildExample(LbTIndvCustInfoQuery lbTIndvCustInfo) {
        LbTIndvCustInfoExample example = new LbTIndvCustInfoExample();
        LbTIndvCustInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTIndvCustInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTIndvCustInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTIndvCustInfo.getCustTyp());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTIndvCustInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTIndvCustInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTIndvCustInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTIndvCustInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTIndvCustInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTIndvCustInfo.getOwnOrganId());
            }
        }
        buildExampleExt(lbTIndvCustInfo, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-个人客户信息ExampleExt方法
     *
     * @param lbTIndvCustInfo
     * @return
     */
    public void buildExampleExt(LbTIndvCustInfoQuery lbTIndvCustInfo, LbTIndvCustInfoExample.Criteria criteria) {

        //自定义实现
    }

}
