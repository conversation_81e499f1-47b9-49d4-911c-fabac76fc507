package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoExample;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 网贷系统-落地表-合同信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface LbSOlCtrInfoMapper extends CommonMapper<LbSOlCtrInfoDo> {

    /**
     * 批量插入合同信息
     *
     * @param lbSOlCtrInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbSOlCtrInfoDo> lbSOlCtrInfoList);

    /**
     * 清空合同信息表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据条件查询数据量
     * 
     * @param example 条件
     * @return long
     */
    long countByExample(LbSOlCtrInfoExample example);
}