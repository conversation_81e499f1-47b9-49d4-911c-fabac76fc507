package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreAdkzhDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreAdkzhMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkzhDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkzhKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkzhExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAdkzhQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 核心系统-历史表-贷款账户主数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHCoreAdkzhDaoImpl extends AbstractBaseDaoImpl<LbHCoreAdkzhDo, LbHCoreAdkzhMapper>
    implements LbHCoreAdkzhDao {
    /**
     * 分页查询
     *
     * @param lbHCoreAdkzh 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCoreAdkzhDo> selectPage(LbHCoreAdkzhQuery lbHCoreAdkzh, PageParam pageParam) {
        LbHCoreAdkzhExample example = buildExample(lbHCoreAdkzh);
        return PageHelper.<LbHCoreAdkzhDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    @Override
    public LbHCoreAdkzhDo selectByKey(String faredm, String daikzh) {
        LbHCoreAdkzhKeyDo lbHCoreAdkzhKeyDo = new LbHCoreAdkzhKeyDo();
        lbHCoreAdkzhKeyDo.setFaredm(faredm);
        lbHCoreAdkzhKeyDo.setDaikzh(daikzh);
        return getMapper().selectByPrimaryKey(lbHCoreAdkzhKeyDo);
    }

    /**
     * 根据key删除核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    @Override
    public int deleteByKey(String faredm, String daikzh) {
        LbHCoreAdkzhKeyDo lbHCoreAdkzhKeyDo = new LbHCoreAdkzhKeyDo();
        lbHCoreAdkzhKeyDo.setFaredm(faredm);
        lbHCoreAdkzhKeyDo.setDaikzh(daikzh);
        return getMapper().deleteByPrimaryKey(lbHCoreAdkzhKeyDo);
    }

    /**
     * 查询核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh 条件
     * @return List<LbHCoreAdkzhDo>
     */
    @Override
    public List<LbHCoreAdkzhDo> selectByExample(LbHCoreAdkzhQuery lbHCoreAdkzh) {
        return getMapper().selectByExample(buildExample(lbHCoreAdkzh));
    }

    /**
     * 新增核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCoreAdkzhDo lbHCoreAdkzh) {
        if (lbHCoreAdkzh == null) {
            return -1;
        }

        return getMapper().insertSelective(lbHCoreAdkzh);
    }

    /**
     * 修改核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh
     * @return
     */
    @Override
    public int updateBySelective(LbHCoreAdkzhDo lbHCoreAdkzh) {
        if (lbHCoreAdkzh == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHCoreAdkzh);
    }

    @Override
    public int updateBySelectiveByExample(LbHCoreAdkzhDo lbHCoreAdkzh, LbHCoreAdkzhQuery lbHCoreAdkzhQuery) {
        return getMapper().updateByExampleSelective(lbHCoreAdkzh, buildExample(lbHCoreAdkzhQuery));
    }

    /**
     * 构建核心系统-历史表-贷款账户主Example信息
     *
     * @param lbHCoreAdkzh
     * @return
     */
    public LbHCoreAdkzhExample buildExample(LbHCoreAdkzhQuery lbHCoreAdkzh) {
        LbHCoreAdkzhExample example = new LbHCoreAdkzhExample();
        LbHCoreAdkzhExample.Criteria criteria = example.createCriteria();
        if (lbHCoreAdkzh != null) {
            //添加查询条件
            if (null != lbHCoreAdkzh.getCsyjfx()) {
                criteria.andCsyjfxEqualTo(lbHCoreAdkzh.getCsyjfx());
            }
            if (null != lbHCoreAdkzh.getHexibj()) {
                criteria.andHexibjEqualTo(lbHCoreAdkzh.getHexibj());
            }
            if (null != lbHCoreAdkzh.getDtlixi()) {
                criteria.andDtlixiEqualTo(lbHCoreAdkzh.getDtlixi());
            }
            if (null != lbHCoreAdkzh.getYinstx()) {
                criteria.andYinstxEqualTo(lbHCoreAdkzh.getYinstx());
            }
            if (null != lbHCoreAdkzh.getYinjtx()) {
                criteria.andYinjtxEqualTo(lbHCoreAdkzh.getYinjtx());
            }
            if (null != lbHCoreAdkzh.getFuxiii()) {
                criteria.andFuxiiiEqualTo(lbHCoreAdkzh.getFuxiii());
            }
            if (null != lbHCoreAdkzh.getYjfuxi()) {
                criteria.andYjfuxiEqualTo(lbHCoreAdkzh.getYjfuxi());
            }
            if (null != lbHCoreAdkzh.getCuisfx()) {
                criteria.andCuisfxEqualTo(lbHCoreAdkzh.getCuisfx());
            }
            if (null != lbHCoreAdkzh.getYinsfx()) {
                criteria.andYinsfxEqualTo(lbHCoreAdkzh.getYinsfx());
            }
            if (null != lbHCoreAdkzh.getHexilx()) {
                criteria.andHexilxEqualTo(lbHCoreAdkzh.getHexilx());
            }
            if (null != lbHCoreAdkzh.getYsyjfx()) {
                criteria.andYsyjfxEqualTo(lbHCoreAdkzh.getYsyjfx());
            }
            if (null != lbHCoreAdkzh.getCuisqx()) {
                criteria.andCuisqxEqualTo(lbHCoreAdkzh.getCuisqx());
            }
            if (null != lbHCoreAdkzh.getYinsqx()) {
                criteria.andYinsqxEqualTo(lbHCoreAdkzh.getYinsqx());
            }
            if (null != lbHCoreAdkzh.getCsyjlx()) {
                criteria.andCsyjlxEqualTo(lbHCoreAdkzh.getCsyjlx());
            }
            if (null != lbHCoreAdkzh.getYsyjlx()) {
                criteria.andYsyjlxEqualTo(lbHCoreAdkzh.getYsyjlx());
            }
            if (null != lbHCoreAdkzh.getDaikjj()) {
                criteria.andDaikjjEqualTo(lbHCoreAdkzh.getDaikjj());
            }
            if (null != lbHCoreAdkzh.getDaizbj()) {
                criteria.andDaizbjEqualTo(lbHCoreAdkzh.getDaizbj());
            }
            if (null != lbHCoreAdkzh.getZhunbj()) {
                criteria.andZhunbjEqualTo(lbHCoreAdkzh.getZhunbj());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCoreAdkzh.getDataDate());
            }
            if (null != lbHCoreAdkzh.getShjnch()) {
                criteria.andShjnchEqualTo(lbHCoreAdkzh.getShjnch());
            }
            if (null != lbHCoreAdkzh.getMxxhao()) {
                criteria.andMxxhaoEqualTo(lbHCoreAdkzh.getMxxhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getBeizhu())) {
                criteria.andBeizhuEqualTo(lbHCoreAdkzh.getBeizhu());
            }
            if (null != lbHCoreAdkzh.getWeihsj()) {
                criteria.andWeihsjEqualTo(lbHCoreAdkzh.getWeihsj());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getXiohgy())) {
                criteria.andXiohgyEqualTo(lbHCoreAdkzh.getXiohgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbHCoreAdkzh.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getKaihgy())) {
                criteria.andKaihgyEqualTo(lbHCoreAdkzh.getKaihgy());
            }
            if (null != lbHCoreAdkzh.getDzhibj()) {
                criteria.andDzhibjEqualTo(lbHCoreAdkzh.getDzhibj());
            }
            if (null != lbHCoreAdkzh.getFajnsr()) {
                criteria.andFajnsrEqualTo(lbHCoreAdkzh.getFajnsr());
            }
            if (null != lbHCoreAdkzh.getYinsfj()) {
                criteria.andYinsfjEqualTo(lbHCoreAdkzh.getYinsfj());
            }
            if (null != lbHCoreAdkzh.getFeiysr()) {
                criteria.andFeiysrEqualTo(lbHCoreAdkzh.getFeiysr());
            }
            if (null != lbHCoreAdkzh.getYinsfy()) {
                criteria.andYinsfyEqualTo(lbHCoreAdkzh.getYinsfy());
            }
            if (null != lbHCoreAdkzh.getLixisr()) {
                criteria.andLixisrEqualTo(lbHCoreAdkzh.getLixisr());
            }
            if (null != lbHCoreAdkzh.getZhhalx()) {
                criteria.andZhhalxEqualTo(lbHCoreAdkzh.getZhhalx());
            }
            if (null != lbHCoreAdkzh.getZhhabj()) {
                criteria.andZhhabjEqualTo(lbHCoreAdkzh.getZhhabj());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getKaihjg())) {
                criteria.andKaihjgEqualTo(lbHCoreAdkzh.getKaihjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getZhjyrq())) {
                criteria.andZhjyrqEqualTo(lbHCoreAdkzh.getZhjyrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDaoqrq())) {
                criteria.andDaoqrqEqualTo(lbHCoreAdkzh.getDaoqrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getQixirq())) {
                criteria.andQixirqEqualTo(lbHCoreAdkzh.getQixirq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getKaihrq())) {
                criteria.andKaihrqEqualTo(lbHCoreAdkzh.getKaihrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDkkjlb())) {
                criteria.andDkkjlbEqualTo(lbHCoreAdkzh.getDkkjlb());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getChapmc())) {
                criteria.andChapmcEqualTo(lbHCoreAdkzh.getChapmc());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getChapdm())) {
                criteria.andChapdmEqualTo(lbHCoreAdkzh.getChapdm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbHCoreAdkzh.getWeihjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getZhixrq())) {
                criteria.andZhixrqEqualTo(lbHCoreAdkzh.getZhixrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getZhngjg())) {
                criteria.andZhngjgEqualTo(lbHCoreAdkzh.getZhngjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getYngyjg())) {
                criteria.andYngyjgEqualTo(lbHCoreAdkzh.getYngyjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getKehzwm())) {
                criteria.andKehzwmEqualTo(lbHCoreAdkzh.getKehzwm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getKehhao())) {
                criteria.andKehhaoEqualTo(lbHCoreAdkzh.getKehhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getHtngbh())) {
                criteria.andHtngbhEqualTo(lbHCoreAdkzh.getHtngbh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDkjeju())) {
                criteria.andDkjejuEqualTo(lbHCoreAdkzh.getDkjeju());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDaikzh())) {
                criteria.andDaikzhEqualTo(lbHCoreAdkzh.getDaikzh());
            }
            if (null != lbHCoreAdkzh.getDbkksx()) {
                criteria.andDbkksxEqualTo(lbHCoreAdkzh.getDbkksx());
            }
            if (null != lbHCoreAdkzh.getYuqibj()) {
                criteria.andYuqibjEqualTo(lbHCoreAdkzh.getYuqibj());
            }
            if (null != lbHCoreAdkzh.getZhchbj()) {
                criteria.andZhchbjEqualTo(lbHCoreAdkzh.getZhchbj());
            }
            if (null != lbHCoreAdkzh.getKfngje()) {
                criteria.andKfngjeEqualTo(lbHCoreAdkzh.getKfngje());
            }
            if (null != lbHCoreAdkzh.getDjkfje()) {
                criteria.andDjkfjeEqualTo(lbHCoreAdkzh.getDjkfje());
            }
            if (null != lbHCoreAdkzh.getYfngje()) {
                criteria.andYfngjeEqualTo(lbHCoreAdkzh.getYfngje());
            }
            if (null != lbHCoreAdkzh.getJiejje()) {
                criteria.andJiejjeEqualTo(lbHCoreAdkzh.getJiejje());
            }
            if (null != lbHCoreAdkzh.getHtngje()) {
                criteria.andHtngjeEqualTo(lbHCoreAdkzh.getHtngje());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getHuobdh())) {
                criteria.andHuobdhEqualTo(lbHCoreAdkzh.getHuobdh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getFaredm())) {
                criteria.andFaredmEqualTo(lbHCoreAdkzh.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getJiluzt())) {
                criteria.andJiluztEqualTo(lbHCoreAdkzh.getJiluzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDkzhzt())) {
                criteria.andDkzhztEqualTo(lbHCoreAdkzh.getDkzhzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getYngjzt())) {
                criteria.andYngjztEqualTo(lbHCoreAdkzh.getYngjzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getDaikxt())) {
                criteria.andDaikxtEqualTo(lbHCoreAdkzh.getDaikxt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getQixian())) {
                criteria.andQixianEqualTo(lbHCoreAdkzh.getQixian());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getXiohrq())) {
                criteria.andXiohrqEqualTo(lbHCoreAdkzh.getXiohrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkzh.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbHCoreAdkzh.getWeihrq());
            }
        }
        buildExampleExt(lbHCoreAdkzh, criteria);
        return example;
    }

    /**
     * 构建核心系统-历史表-贷款账户主ExampleExt方法
     *
     * @param lbHCoreAdkzh
     * @return
     */
    public void buildExampleExt(LbHCoreAdkzhQuery lbHCoreAdkzh, LbHCoreAdkzhExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzhList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbHCoreAdkzhDo> lbHCoreAdkzhList) {
        if (lbHCoreAdkzhList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbHCoreAdkzhList);
    }

    /**
     * 清空核心系统-历史表-贷款账户主所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
