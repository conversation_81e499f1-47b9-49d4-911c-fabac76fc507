package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSOlLoanInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlLoanInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-落地表-借据信息（记录客户借款信息）数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbSOlLoanInfoDaoImpl extends AbstractBaseDaoImpl<LbSOlLoanInfoDo, LbSOlLoanInfoMapper>
    implements LbSOlLoanInfoDao {
    /**
     * 分页查询
     *
     * @param lbSOlLoanInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSOlLoanInfoDo> selectPage(LbSOlLoanInfoQuery lbSOlLoanInfo, PageParam pageParam) {
        LbSOlLoanInfoExample example = buildExample(lbSOlLoanInfo);
        return PageHelper.<LbSOlLoanInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-落地表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @return
     */
    @Override
    public LbSOlLoanInfoDo selectByKey(String loanApplyId) {
        LbSOlLoanInfoKeyDo lbSOlLoanInfoKeyDo = new LbSOlLoanInfoKeyDo();
        lbSOlLoanInfoKeyDo.setLoanApplyId(loanApplyId);
        lbSOlLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbSOlLoanInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-落地表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @return
     */
    @Override
    public int deleteByKey(String loanApplyId) {
        LbSOlLoanInfoKeyDo lbSOlLoanInfoKeyDo = new LbSOlLoanInfoKeyDo();
        lbSOlLoanInfoKeyDo.setLoanApplyId(loanApplyId);
        lbSOlLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbSOlLoanInfoKeyDo);
    }

    /**
     * 查询网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo 条件
     * @return List<LbSOlLoanInfoDo>
     */
    @Override
    public List<LbSOlLoanInfoDo> selectByExample(LbSOlLoanInfoQuery lbSOlLoanInfo) {
        return getMapper().selectByExample(buildExample(lbSOlLoanInfo));
    }

    /**
     * 新增网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSOlLoanInfoDo lbSOlLoanInfo) {
        if (lbSOlLoanInfo == null) {
            return -1;
        }
        lbSOlLoanInfo.setChannelNo(AppParamUtil.getChannelNo());

        lbSOlLoanInfo.setCreateTime(BusinessDateUtil.getDate());
        lbSOlLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbSOlLoanInfo);
    }

    /**
     * 修改网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo
     * @return
     */
    @Override
    public int updateBySelective(LbSOlLoanInfoDo lbSOlLoanInfo) {
        if (lbSOlLoanInfo == null) {
            return -1;
        }
        lbSOlLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbSOlLoanInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbSOlLoanInfoDo lbSOlLoanInfo, LbSOlLoanInfoQuery lbSOlLoanInfoQuery) {
        lbSOlLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbSOlLoanInfo, buildExample(lbSOlLoanInfoQuery));
    }

    /**
     * 构建网贷系统-落地表-借据信息（记录客户借款信息）Example信息
     *
     * @param lbSOlLoanInfo
     * @return
     */
    public LbSOlLoanInfoExample buildExample(LbSOlLoanInfoQuery lbSOlLoanInfo) {
        LbSOlLoanInfoExample example = new LbSOlLoanInfoExample();
        LbSOlLoanInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbSOlLoanInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbSOlLoanInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getChannelNo())) {
                criteria.andChannelNoEqualTo(lbSOlLoanInfo.getChannelNo());
            }
            if (null != lbSOlLoanInfo.getInstallmentAmount()) {
                criteria.andInstallmentAmountEqualTo(lbSOlLoanInfo.getInstallmentAmount());
            }
            if (null != lbSOlLoanInfo.getInstallmentNum()) {
                criteria.andInstallmentNumEqualTo(lbSOlLoanInfo.getInstallmentNum());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getStatus())) {
                criteria.andStatusEqualTo(lbSOlLoanInfo.getStatus());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getBusinessSign())) {
                criteria.andBusinessSignEqualTo(lbSOlLoanInfo.getBusinessSign());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getClassification())) {
                criteria.andClassificationEqualTo(lbSOlLoanInfo.getClassification());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getMarketCenterId())) {
                criteria.andMarketCenterIdEqualTo(lbSOlLoanInfo.getMarketCenterId());
            }
            if (null != lbSOlLoanInfo.getSettleDateBegin()) {
                criteria.andSettleDateGreaterThanOrEqualTo(lbSOlLoanInfo.getSettleDateBegin());
            }
            if (null != lbSOlLoanInfo.getSettleDateEnd()) {
                criteria.andSettleDateLessThanOrEqualTo(lbSOlLoanInfo.getSettleDateEnd());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbSOlLoanInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getStoreName())) {
                criteria.andStoreNameEqualTo(lbSOlLoanInfo.getStoreName());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getCustMgrId())) {
                criteria.andCustMgrIdEqualTo(lbSOlLoanInfo.getCustMgrId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getCustMgrOrganId())) {
                criteria.andCustMgrOrganIdEqualTo(lbSOlLoanInfo.getCustMgrOrganId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getWithholdProtocolId())) {
                criteria.andWithholdProtocolIdEqualTo(lbSOlLoanInfo.getWithholdProtocolId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getContractId())) {
                criteria.andContractIdEqualTo(lbSOlLoanInfo.getContractId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getCreditApplyId())) {
                criteria.andCreditApplyIdEqualTo(lbSOlLoanInfo.getCreditApplyId());
            }
            if (null != lbSOlLoanInfo.getLoanEndTimeBegin()) {
                criteria.andLoanEndTimeGreaterThanOrEqualTo(lbSOlLoanInfo.getLoanEndTimeBegin());
            }
            if (null != lbSOlLoanInfo.getLoanEndTimeEnd()) {
                criteria.andLoanEndTimeLessThanOrEqualTo(lbSOlLoanInfo.getLoanEndTimeEnd());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getLoanApplyId())) {
                criteria.andLoanApplyIdEqualTo(lbSOlLoanInfo.getLoanApplyId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getUserId())) {
                criteria.andUserIdEqualTo(lbSOlLoanInfo.getUserId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getUserName())) {
                criteria.andUserNameEqualTo(lbSOlLoanInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getUserTel())) {
                criteria.andUserTelEqualTo(lbSOlLoanInfo.getUserTel());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getCertificateType())) {
                criteria.andCertificateTypeEqualTo(lbSOlLoanInfo.getCertificateType());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getCertificateNo())) {
                criteria.andCertificateNoEqualTo(lbSOlLoanInfo.getCertificateNo());
            }
            if (null != lbSOlLoanInfo.getLoanAmount()) {
                criteria.andLoanAmountEqualTo(lbSOlLoanInfo.getLoanAmount());
            }
            if (null != lbSOlLoanInfo.getRate()) {
                criteria.andRateEqualTo(lbSOlLoanInfo.getRate());
            }
            if (null != lbSOlLoanInfo.getLoanStartTimeBegin()) {
                criteria.andLoanStartTimeGreaterThanOrEqualTo(lbSOlLoanInfo.getLoanStartTimeBegin());
            }
            if (null != lbSOlLoanInfo.getLoanStartTimeEnd()) {
                criteria.andLoanStartTimeLessThanOrEqualTo(lbSOlLoanInfo.getLoanStartTimeEnd());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getLoanInvoiceId())) {
                criteria.andLoanInvoiceIdEqualTo(lbSOlLoanInfo.getLoanInvoiceId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getLoanType())) {
                criteria.andLoanTypeEqualTo(lbSOlLoanInfo.getLoanType());
            }
            if (null != lbSOlLoanInfo.getRepayDay()) {
                criteria.andRepayDayEqualTo(lbSOlLoanInfo.getRepayDay());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getProductId())) {
                criteria.andProductIdEqualTo(lbSOlLoanInfo.getProductId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getProductName())) {
                criteria.andProductNameEqualTo(lbSOlLoanInfo.getProductName());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getProductCatalog())) {
                criteria.andProductCatalogEqualTo(lbSOlLoanInfo.getProductCatalog());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getMerchantId())) {
                criteria.andMerchantIdEqualTo(lbSOlLoanInfo.getMerchantId());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getMerchantName())) {
                criteria.andMerchantNameEqualTo(lbSOlLoanInfo.getMerchantName());
            }
            if (StringUtil.isNotEmpty(lbSOlLoanInfo.getStoreId())) {
                criteria.andStoreIdEqualTo(lbSOlLoanInfo.getStoreId());
            }
        }
        buildExampleExt(lbSOlLoanInfo, criteria);
        return example;
    }

    /**
     * 清空借据信息表所有数据
     * 用于文件同步前的数据清理
     *
     * @return 删除的记录数
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    /**
     * 批量插入借据信息
     * 用于文件同步的批量数据导入
     *
     * @param lbSOlLoanInfoList 借据信息列表
     * @return 插入的记录数
     */
    @Override
    public int insertList(List<LbSOlLoanInfoDo> lbSOlLoanInfoList) {
        if (lbSOlLoanInfoList == null || lbSOlLoanInfoList.isEmpty()) {
            return 0;
        }
        
        // 设置公共字段
        Date currentTime = BusinessDateUtil.getDate();
        String tenantId = AppParamUtil.getTenantId();
        String channelNo = AppParamUtil.getChannelNo();
        
        for (LbSOlLoanInfoDo lbSOlLoanInfo : lbSOlLoanInfoList) {
            if (lbSOlLoanInfo != null) {
                lbSOlLoanInfo.setTenantId(tenantId);
                lbSOlLoanInfo.setChannelNo(channelNo);
                if (lbSOlLoanInfo.getCreateTime() == null) {
                    lbSOlLoanInfo.setCreateTime(currentTime);
                }
                if (lbSOlLoanInfo.getUpdateTime() == null) {
                    lbSOlLoanInfo.setUpdateTime(currentTime);
                }
            }
        }
        
        return getMapper().insertList(lbSOlLoanInfoList);
    }

    /**
     * 构建网贷系统-落地表-借据信息（记录客户借款信息）ExampleExt方法
     *
     * @param lbSOlLoanInfo
     * @return
     */
    public void buildExampleExt(LbSOlLoanInfoQuery lbSOlLoanInfo, LbSOlLoanInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSOlLoanInfoDo selectFirstOne(LbSOlLoanInfoQuery query) {
        LbSOlLoanInfoExample example = buildExample(query);
        example.setOrderByClause("loan_apply_id ASC, tenant_id ASC");
        List<LbSOlLoanInfoDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSOlLoanInfoQuery query) {
        LbSOlLoanInfoExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

}
