package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlLoanInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-落地表-借据信息（记录客户借款信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlLoanInfoDao extends IBaseDao<LbSOlLoanInfoDo> {
    /**
     * 分页查询网贷系统-落地表-借据信息（记录客户借款信息）
     *
     * @param lbSOlLoanInfoQuery 条件
     * @return PageInfo<LbSOlLoanInfoDo>
     */
    PageInfo<LbSOlLoanInfoDo> selectPage(LbSOlLoanInfoQuery lbSOlLoanInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-落地表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @return
     */
    LbSOlLoanInfoDo selectByKey(String loanApplyId);

    /**
     * 根据key删除网贷系统-落地表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @return
     */
    int deleteByKey(String loanApplyId);

    /**
     * 查询网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfoQuery 条件
     * @return List<LbSOlLoanInfoDo>
     */
    List<LbSOlLoanInfoDo> selectByExample(LbSOlLoanInfoQuery lbSOlLoanInfoQuery);

    /**
     * 新增网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo 条件
     * @return int>
     */
    int insertBySelective(LbSOlLoanInfoDo lbSOlLoanInfo);

    /**
     * 修改网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo
     * @return
     */
    int updateBySelective(LbSOlLoanInfoDo lbSOlLoanInfo);

    /**
     * 修改网贷系统-落地表-借据信息（记录客户借款信息）信息
     *
     * @param lbSOlLoanInfo
     * @param lbSOlLoanInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSOlLoanInfoDo lbSOlLoanInfo, LbSOlLoanInfoQuery lbSOlLoanInfoQuery);

    /**
     * 清空借据信息表所有数据
     * 用于文件同步前的数据清理
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量插入借据信息
     * 用于文件同步的批量数据导入
     *
     * @param lbSOlLoanInfoList 借据信息列表
     * @return 插入的记录数
     */
    int insertList(List<LbSOlLoanInfoDo> lbSOlLoanInfoList);

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSOlLoanInfoDo selectFirstOne(LbSOlLoanInfoQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSOlLoanInfoQuery query);
}
