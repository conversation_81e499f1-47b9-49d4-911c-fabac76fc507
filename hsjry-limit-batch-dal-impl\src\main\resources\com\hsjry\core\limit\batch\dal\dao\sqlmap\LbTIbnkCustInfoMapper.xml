<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTIbnkCustInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTIbnkCustInfoDo">
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        cust_no
        , cust_typ
        , cust_nm
        , cert_typ
        , cert_no
        , operator_id
        , own_organ_id
        , tenant_id
        , create_time
        , update_time
    </sql>

    <!-- 清空同业客户信息表数据 -->
    <update id="truncateTable">
        truncate table LB_T_IBNK_CUST_INFO
    </update>

    <!-- 从源表LC_CUST_LIMIT_OBJECT_INFO导入同业客户信息数据 -->
    <insert id="insertFromSource">
        insert into lb_t_ibnk_cust_info(cust_no, cust_typ, cust_nm, cert_typ, cert_no, operator_id, own_organ_id, tenant_id,
                                        create_time, update_time)
        select distinct lcloi.USER_ID,
                        lcloi.USER_TYPE,
                        lcloi.USER_NAME,
                        lcloi.USER_CERTIFICATE_KIND,
                        lcloi.USER_CERTIFICATE_NO,
                        'ESB_USER' as operator_id,
                        'DEFAULT_ORGAN' as own_organ_id,
                        lcloi.TENANT_ID,
                        sysdate,
                        sysdate
        from LC_CUST_LIMIT_OBJECT_INFO lcloi
        where lcloi.USER_TYPE = '110' and lcloi.USER_CERTIFICATE_KIND = '610063' and lcloi.USER_CERTIFICATE_NO is not null
        and length(lcloi.USER_CERTIFICATE_NO) = 18
    </insert>

    <!-- 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入同业客户信息数据 -->
    <insert id="insertFromSourceByUserIdList">
        <if test="list != null and list.size() > 0">
            insert into lb_t_ibnk_cust_info(cust_no, cust_typ, cust_nm, cert_typ, cert_no, operator_id, own_organ_id,
            tenant_id,
            create_time, update_time)
            select distinct lcloi.USER_ID,
            lcloi.USER_TYPE,
            lcloi.USER_NAME,
            lcloi.USER_CERTIFICATE_KIND,
            lcloi.USER_CERTIFICATE_NO,
            'ESB_USER' as operator_id,
            'DEFAULT_ORGAN' as own_organ_id,
            lcloi.TENANT_ID,
            sysdate,
            sysdate
            from LC_CUST_LIMIT_OBJECT_INFO lcloi
            where lcloi.USER_TYPE = '110' and lcloi.USER_CERTIFICATE_KIND = '610063' and lcloi.USER_CERTIFICATE_NO is
            not null
            and length(lcloi.USER_CERTIFICATE_NO) = 18
            and lcloi.USER_ID in
            <foreach collection="list" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </insert>
</mapper>