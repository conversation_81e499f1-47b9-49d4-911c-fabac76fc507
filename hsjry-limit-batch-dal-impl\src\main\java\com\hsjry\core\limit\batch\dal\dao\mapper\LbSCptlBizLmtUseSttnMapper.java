package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnExample;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 资金系统-落地表-业务限额使用情况mapper
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface LbSCptlBizLmtUseSttnMapper extends CommonMapper<LbSCptlBizLmtUseSttnDo> {

    /**
     * 批量插入业务限额使用情况
     *
     * @param lbSCptlBizLmtUseSttnList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbSCptlBizLmtUseSttnDo> lbSCptlBizLmtUseSttnList);

    /**
     * 清空业务限额使用情况表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据条件查询数据量
     * 
     * @param example 条件
     * @return long
     */
    long countByExample(LbSCptlBizLmtUseSttnExample example);
}