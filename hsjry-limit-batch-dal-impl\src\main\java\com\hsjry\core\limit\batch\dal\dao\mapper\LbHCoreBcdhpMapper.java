package com.hsjry.core.limit.batch.dal.dao.mapper;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

import java.util.List;

/**
 * 核心系统-落地表-银行承兑汇票产品定义mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCoreBcdhpMapper extends CommonMapper<LbHCoreBcdhpDo> {
    int deleteAll();
    
    /**
     * 根据数据日期删除银承汇票历史表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(@Param("dataDate") String dataDate);

    /**
     * 批量插入银承汇票历史信息
     *
     * @param list 银承汇票历史信息列表
     * @return int
     */
    int insertList(@Param("list") List<LbHCoreBcdhpDo> list);
}
