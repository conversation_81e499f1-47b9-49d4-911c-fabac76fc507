package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblTfrdcnLmtOcpDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblTfrdcnLmtOcpMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtOcpKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtOcpExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblTfrdcnLmtOcpQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 电票系统-历史表-日终转贴现额度占用同步数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHElcblTfrdcnLmtOcpDaoImpl extends AbstractBaseDaoImpl<LbHElcblTfrdcnLmtOcpDo, LbHElcblTfrdcnLmtOcpMapper>
    implements LbHElcblTfrdcnLmtOcpDao {
    /**
     * 分页查询
     *
     * @param lbHElcblTfrdcnLmtOcp 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHElcblTfrdcnLmtOcpDo> selectPage(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcp,
        PageParam pageParam) {
        LbHElcblTfrdcnLmtOcpExample example = buildExample(lbHElcblTfrdcnLmtOcp);
        return PageHelper.<LbHElcblTfrdcnLmtOcpDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-历史表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public LbHElcblTfrdcnLmtOcpDo selectByKey(String batchNumb, String billNumb, String billRangeStart,
        String billRangeEnd, String dataDate) {
        LbHElcblTfrdcnLmtOcpKeyDo lbHElcblTfrdcnLmtOcpKeyDo = new LbHElcblTfrdcnLmtOcpKeyDo();
        lbHElcblTfrdcnLmtOcpKeyDo.setBatchNumb(batchNumb);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillNumb(billNumb);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblTfrdcnLmtOcpKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHElcblTfrdcnLmtOcpKeyDo);
    }

    /**
     * 根据key删除电票系统-历史表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd,
        String dataDate) {
        LbHElcblTfrdcnLmtOcpKeyDo lbHElcblTfrdcnLmtOcpKeyDo = new LbHElcblTfrdcnLmtOcpKeyDo();
        lbHElcblTfrdcnLmtOcpKeyDo.setBatchNumb(batchNumb);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillNumb(billNumb);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblTfrdcnLmtOcpKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblTfrdcnLmtOcpKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHElcblTfrdcnLmtOcpKeyDo);
    }

    /**
     * 查询电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp 条件
     * @return List<LbHElcblTfrdcnLmtOcpDo>
     */
    @Override
    public List<LbHElcblTfrdcnLmtOcpDo> selectByExample(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcp) {
        return getMapper().selectByExample(buildExample(lbHElcblTfrdcnLmtOcp));
    }

    /**
     * 新增电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp) {
        if (lbHElcblTfrdcnLmtOcp == null) {
            return -1;
        }
        return getMapper().insertSelective(lbHElcblTfrdcnLmtOcp);
    }

    /**
     * 修改电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp
     * @return
     */
    @Override
    public int updateBySelective(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp) {
        if (lbHElcblTfrdcnLmtOcp == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHElcblTfrdcnLmtOcp);
    }

    @Override
    public int updateBySelectiveByExample(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp,
        LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcpQuery) {
        return getMapper().updateByExampleSelective(lbHElcblTfrdcnLmtOcp, buildExample(lbHElcblTfrdcnLmtOcpQuery));
    }

    /**
     * 构建电票系统-历史表-日终转贴现额度占用同步Example信息
     *
     * @param lbHElcblTfrdcnLmtOcp
     * @return
     */
    public LbHElcblTfrdcnLmtOcpExample buildExample(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcp) {
        LbHElcblTfrdcnLmtOcpExample example = new LbHElcblTfrdcnLmtOcpExample();
        LbHElcblTfrdcnLmtOcpExample.Criteria criteria = example.createCriteria();
        if (lbHElcblTfrdcnLmtOcp != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getSmBsnUserCertificateNo())) {
                criteria.andSmBsnUserCertificateNoEqualTo(lbHElcblTfrdcnLmtOcp.getSmBsnUserCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getDataDate())) {
                criteria.andDataDateEqualTo(lbHElcblTfrdcnLmtOcp.getDataDate());
            }
            if (null != lbHElcblTfrdcnLmtOcp.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbHElcblTfrdcnLmtOcp.getUseOccupyAmount());
            }
            if (null != lbHElcblTfrdcnLmtOcp.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbHElcblTfrdcnLmtOcp.getAvailableAmount());
            }
            if (null != lbHElcblTfrdcnLmtOcp.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbHElcblTfrdcnLmtOcp.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getSmBsnLimitType())) {
                criteria.andSmBsnLimitTypeEqualTo(lbHElcblTfrdcnLmtOcp.getSmBsnLimitType());
            }
            if (null != lbHElcblTfrdcnLmtOcp.getBillAmount()) {
                criteria.andBillAmountEqualTo(lbHElcblTfrdcnLmtOcp.getBillAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getCurrency())) {
                criteria.andCurrencyEqualTo(lbHElcblTfrdcnLmtOcp.getCurrency());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getCustMgrNm())) {
                criteria.andCustMgrNmEqualTo(lbHElcblTfrdcnLmtOcp.getCustMgrNm());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getCustMgrNumb())) {
                criteria.andCustMgrNumbEqualTo(lbHElcblTfrdcnLmtOcp.getCustMgrNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getBatchNumb())) {
                criteria.andBatchNumbEqualTo(lbHElcblTfrdcnLmtOcp.getBatchNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getSmBsnUserCertificateKind())) {
                criteria.andSmBsnUserCertificateKindEqualTo(lbHElcblTfrdcnLmtOcp.getSmBsnUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getSmBsnUserId())) {
                criteria.andSmBsnUserIdEqualTo(lbHElcblTfrdcnLmtOcp.getSmBsnUserId());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbHElcblTfrdcnLmtOcp.getCoreInstNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getBranchNo())) {
                criteria.andBranchNoEqualTo(lbHElcblTfrdcnLmtOcp.getBranchNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getRelationId())) {
                criteria.andRelationIdEqualTo(lbHElcblTfrdcnLmtOcp.getRelationId());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getBillRangeEnd())) {
                criteria.andBillRangeEndEqualTo(lbHElcblTfrdcnLmtOcp.getBillRangeEnd());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getBillRangeStart())) {
                criteria.andBillRangeStartEqualTo(lbHElcblTfrdcnLmtOcp.getBillRangeStart());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getBillNumb())) {
                criteria.andBillNumbEqualTo(lbHElcblTfrdcnLmtOcp.getBillNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtOcp.getProductId())) {
                criteria.andProductIdEqualTo(lbHElcblTfrdcnLmtOcp.getProductId());
            }
        }
        buildExampleExt(lbHElcblTfrdcnLmtOcp, criteria);
        return example;
    }

    /**
     * 构建电票系统-历史表-日终转贴现额度占用同步ExampleExt方法
     *
     * @param lbHElcblTfrdcnLmtOcp
     * @return
     */
    public void buildExampleExt(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcp,
        LbHElcblTfrdcnLmtOcpExample.Criteria criteria) {

        //自定义实现
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
