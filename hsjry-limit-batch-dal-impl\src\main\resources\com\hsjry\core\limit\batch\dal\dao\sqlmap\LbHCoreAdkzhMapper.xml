<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreAdkzhMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkzhDo">
        <result property="csyjfx" column="csyjfx" jdbcType="DECIMAL"/> <!-- 催收应计罚息 -->
        <result property="hexibj" column="hexibj" jdbcType="DECIMAL"/> <!-- 核销本金 -->
        <result property="dtlixi" column="dtlixi" jdbcType="DECIMAL"/> <!-- 待摊利息 -->
        <result property="yinstx" column="yinstx" jdbcType="DECIMAL"/> <!-- 应收贴息 -->
        <result property="yinjtx" column="yinjtx" jdbcType="DECIMAL"/> <!-- 应计贴息 -->
        <result property="fuxiii" column="fuxiii" jdbcType="DECIMAL"/> <!-- 复息 -->
        <result property="yjfuxi" column="yjfuxi" jdbcType="DECIMAL"/> <!-- 应计复息 -->
        <result property="cuisfx" column="cuisfx" jdbcType="DECIMAL"/> <!-- 催收罚息 -->
        <result property="yinsfx" column="yinsfx" jdbcType="DECIMAL"/> <!-- 应收罚息 -->
        <result property="hexilx" column="hexilx" jdbcType="DECIMAL"/> <!-- 核销利息 -->
        <result property="ysyjfx" column="ysyjfx" jdbcType="DECIMAL"/> <!-- 应收应计罚息 -->
        <result property="cuisqx" column="cuisqx" jdbcType="DECIMAL"/> <!-- 催收欠息 -->
        <result property="yinsqx" column="yinsqx" jdbcType="DECIMAL"/> <!-- 应收欠息 -->
        <result property="csyjlx" column="csyjlx" jdbcType="DECIMAL"/> <!-- 催收应计利息 -->
        <result property="ysyjlx" column="ysyjlx" jdbcType="DECIMAL"/> <!-- 应收应计利息 -->
        <result property="daikjj" column="daikjj" jdbcType="DECIMAL"/> <!-- 贷款基金 -->
        <result property="daizbj" column="daizbj" jdbcType="DECIMAL"/> <!-- 呆账本金 -->
        <result property="zhunbj" column="zhunbj" jdbcType="DECIMAL"/> <!-- 准备金 -->
        <result property="dataDate" column="data_date" jdbcType="VARCHAR"/> <!-- 数据日期 -->
        <result property="shjnch" column="shjnch" jdbcType="DECIMAL"/> <!-- 时间戳 -->
        <result property="mxxhao" column="mxxhao" jdbcType="DECIMAL"/> <!-- 明细序号 -->
        <result property="beizhu" column="beizhu" jdbcType="VARCHAR"/> <!-- 备注 -->
        <result property="weihsj" column="weihsj" jdbcType="DECIMAL"/> <!-- 维护时间 -->
        <result property="xiohgy" column="xiohgy" jdbcType="VARCHAR"/> <!-- 销户柜员 -->
        <result property="weihgy" column="weihgy" jdbcType="VARCHAR"/> <!-- 维护柜员 -->
        <result property="kaihgy" column="kaihgy" jdbcType="VARCHAR"/> <!-- 开户柜员 -->
        <result property="dzhibj" column="dzhibj" jdbcType="DECIMAL"/> <!-- 呆滞本金 -->
        <result property="fajnsr" column="fajnsr" jdbcType="DECIMAL"/> <!-- 罚金收入 -->
        <result property="yinsfj" column="yinsfj" jdbcType="DECIMAL"/> <!-- 应收罚金 -->
        <result property="feiysr" column="feiysr" jdbcType="DECIMAL"/> <!-- 费用收入 -->
        <result property="yinsfy" column="yinsfy" jdbcType="DECIMAL"/> <!-- 应收费用 -->
        <result property="lixisr" column="lixisr" jdbcType="DECIMAL"/> <!-- 利息收入 -->
        <result property="zhhalx" column="zhhalx" jdbcType="DECIMAL"/> <!-- 置换利息 -->
        <result property="zhhabj" column="zhhabj" jdbcType="DECIMAL"/> <!-- 置换本金 -->
        <result property="kaihjg" column="kaihjg" jdbcType="VARCHAR"/> <!-- 开户机构 -->
        <result property="zhjyrq" column="zhjyrq" jdbcType="VARCHAR"/> <!-- 最后财务交易日 -->
        <result property="daoqrq" column="daoqrq" jdbcType="VARCHAR"/> <!-- 到期日期 -->
        <result property="qixirq" column="qixirq" jdbcType="VARCHAR"/> <!-- 起息日期 -->
        <result property="kaihrq" column="kaihrq" jdbcType="VARCHAR"/> <!-- 开户日期 -->
        <result property="dkkjlb" column="dkkjlb" jdbcType="VARCHAR"/> <!-- 会计类别 -->
        <result property="chapmc" column="chapmc" jdbcType="VARCHAR"/> <!-- 产品名称 -->
        <result property="chapdm" column="chapdm" jdbcType="VARCHAR"/> <!-- 产品代码 -->
        <result property="weihjg" column="weihjg" jdbcType="VARCHAR"/> <!-- 维护机构 -->
        <result property="zhixrq" column="zhixrq" jdbcType="VARCHAR"/> <!-- 止息日期 -->
        <result property="zhngjg" column="zhngjg" jdbcType="VARCHAR"/> <!-- 账务机构 -->
        <result property="yngyjg" column="yngyjg" jdbcType="VARCHAR"/> <!-- 营业机构 -->
        <result property="kehzwm" column="kehzwm" jdbcType="VARCHAR"/> <!-- 客户名 -->
        <result property="kehhao" column="kehhao" jdbcType="VARCHAR"/> <!-- 客户号 -->
        <result property="htngbh" column="htngbh" jdbcType="VARCHAR"/> <!-- 合同编号 -->
        <result property="dkjeju" column="dkjeju" jdbcType="VARCHAR"/> <!-- 贷款借据号 -->
        <result property="daikzh" column="daikzh" jdbcType="VARCHAR"/> <!-- 贷款账号 -->
        <result property="dbkksx" column="dbkksx" jdbcType="DECIMAL"/> <!-- 多笔贷款扣款顺序 -->
        <result property="yuqibj" column="yuqibj" jdbcType="DECIMAL"/> <!-- 逾期本金 -->
        <result property="zhchbj" column="zhchbj" jdbcType="DECIMAL"/> <!-- 正常本金 -->
        <result property="kfngje" column="kfngje" jdbcType="DECIMAL"/> <!-- 可发放金额 -->
        <result property="djkfje" column="djkfje" jdbcType="DECIMAL"/> <!-- 冻结可放金额 -->
        <result property="yfngje" column="yfngje" jdbcType="DECIMAL"/> <!-- 已发放金额 -->
        <result property="jiejje" column="jiejje" jdbcType="DECIMAL"/> <!-- 借据金额 -->
        <result property="htngje" column="htngje" jdbcType="DECIMAL"/> <!-- 合同金额 -->
        <result property="huobdh" column="huobdh" jdbcType="VARCHAR"/> <!-- 货币代号 -->
        <result property="faredm" column="faredm" jdbcType="VARCHAR"/> <!-- 法人代码 -->
        <result property="jiluzt" column="jiluzt" jdbcType="VARCHAR"/> <!-- 记录状态 -->
        <result property="dkzhzt" column="dkzhzt"
                jdbcType="VARCHAR"/> <!-- 贷款账户状态,0-正常,1-销户,2-已核销,3-准销户,4-录入,5-已置换,6-冻结 -->
        <result property="yngjzt" column="yngjzt" jdbcType="VARCHAR"/> <!-- 应计非应计状态,0-应计,1-非应计 -->
        <result property="daikxt" column="daikxt" jdbcType="VARCHAR"/> <!-- 贷款形态,0-正常,1-逾期,2-呆滞,3-呆账 -->
        <result property="qixian" column="qixian" jdbcType="VARCHAR"/> <!-- 期限 -->
        <result property="xiohrq" column="xiohrq" jdbcType="VARCHAR"/> <!-- 销户日期 -->
        <result property="weihrq" column="weihrq" jdbcType="VARCHAR"/> <!-- 维护日期 -->
    </resultMap>
    <sql id="Base_Column_List">
        csyjfx
        , hexibj
                , dtlixi
                , yinstx
                , yinjtx
                , fuxiii
                , yjfuxi
                , cuisfx
                , yinsfx
                , hexilx
                , ysyjfx
                , cuisqx
                , yinsqx
                , csyjlx
                , ysyjlx
                , daikjj
                , daizbj
                , zhunbj
                , data_date
                , shjnch
                , mxxhao
                , beizhu
                , weihsj
                , xiohgy
                , weihgy
                , kaihgy
                , dzhibj
                , fajnsr
                , yinsfj
                , feiysr
                , yinsfy
                , lixisr
                , zhhalx
                , zhhabj
                , kaihjg
                , zhjyrq
                , daoqrq
                , qixirq
                , kaihrq
                , dkkjlb
                , chapmc
                , chapdm
                , weihjg
                , zhixrq
                , zhngjg
                , yngyjg
                , kehzwm
                , kehhao
                , htngbh
                , dkjeju
                , daikzh
                , dbkksx
                , yuqibj
                , zhchbj
                , kfngje
                , djkfje
                , yfngje
                , jiejje
                , htngje
                , huobdh
                , faredm
                , jiluzt
                , dkzhzt
                , yngjzt
                , daikxt
                , qixian
                , xiohrq
                , weihrq
    </sql>
    
    <!-- 批量插入贷款账户主历史数据 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO lb_h_core_adkzh (
                faredm, daikzh, dkjeju, htngbh, kehhao, kehzwm, yngyjg, zhngjg, zhixrq, weihjg, chapdm, chapmc, dkkjlb, kaihrq, qixirq, daoqrq, zhjyrq, kaihjg, zhhabj, zhhalx, lixisr, yinsfy, feiysr, yinsfj, kaihgy, weihgy, xiohgy, weihsj, beizhu, mxxhao, shjnch, data_date, zhunbj, daizbj, daikjj, ysyjlx, csyjlx, yinsqx, cuisqx, ysyjfx, hexilx, yinsfx, cuisfx, yjfuxi, fuxiii, yinjtx, yinstx, dtlixi, hexibj, csyjfx, zhchbj, yuqibj, dbkksx, huobdh, htngje, jiejje, yfngje, djkfje, kfngje, jiluzt, dkzhzt, yngjzt, daikxt, qixian, xiohrq, weihrq, dzhibj, fajnsr, yinsfj
            ) VALUES (
                #{item.faredm, jdbcType=VARCHAR},
                #{item.daikzh, jdbcType=VARCHAR},
                #{item.dkjeju, jdbcType=VARCHAR},
                #{item.htngbh, jdbcType=VARCHAR},
                #{item.kehhao, jdbcType=VARCHAR},
                #{item.kehzwm, jdbcType=VARCHAR},
                #{item.yngyjg, jdbcType=VARCHAR},
                #{item.zhngjg, jdbcType=VARCHAR},
                #{item.zhixrq, jdbcType=VARCHAR},
                #{item.weihjg, jdbcType=VARCHAR},
                #{item.chapdm, jdbcType=VARCHAR},
                #{item.chapmc, jdbcType=VARCHAR},
                #{item.dkkjlb, jdbcType=VARCHAR},
                #{item.kaihrq, jdbcType=VARCHAR},
                #{item.qixirq, jdbcType=VARCHAR},
                #{item.daoqrq, jdbcType=VARCHAR},
                #{item.zhjyrq, jdbcType=VARCHAR},
                #{item.kaihjg, jdbcType=VARCHAR},
                #{item.zhhabj, jdbcType=DECIMAL},
                #{item.zhhalx, jdbcType=DECIMAL},
                #{item.lixisr, jdbcType=DECIMAL},
                #{item.yinsfy, jdbcType=DECIMAL},
                #{item.feiysr, jdbcType=DECIMAL},
                #{item.yinsfj, jdbcType=DECIMAL},
                #{item.kaihgy, jdbcType=VARCHAR},
                #{item.weihgy, jdbcType=VARCHAR},
                #{item.xiohgy, jdbcType=VARCHAR},
                #{item.weihsj, jdbcType=DECIMAL},
                #{item.beizhu, jdbcType=VARCHAR},
                #{item.mxxhao, jdbcType=DECIMAL},
                #{item.shjnch, jdbcType=DECIMAL},
                #{item.dataDate, jdbcType=VARCHAR},
                #{item.zhunbj, jdbcType=DECIMAL},
                #{item.daizbj, jdbcType=DECIMAL},
                #{item.daikjj, jdbcType=DECIMAL},
                #{item.ysyjlx, jdbcType=DECIMAL},
                #{item.csyjlx, jdbcType=DECIMAL},
                #{item.yinsqx, jdbcType=DECIMAL},
                #{item.cuisqx, jdbcType=DECIMAL},
                #{item.ysyjfx, jdbcType=DECIMAL},
                #{item.hexilx, jdbcType=DECIMAL},
                #{item.yinsfx, jdbcType=DECIMAL},
                #{item.cuisfx, jdbcType=DECIMAL},
                #{item.yjfuxi, jdbcType=DECIMAL},
                #{item.fuxiii, jdbcType=DECIMAL},
                #{item.yinjtx, jdbcType=DECIMAL},
                #{item.yinstx, jdbcType=DECIMAL},
                #{item.dtlixi, jdbcType=DECIMAL},
                #{item.hexibj, jdbcType=DECIMAL},
                #{item.csyjfx, jdbcType=DECIMAL},
                #{item.zhchbj, jdbcType=DECIMAL},
                #{item.yuqibj, jdbcType=DECIMAL},
                #{item.dbkksx, jdbcType=VARCHAR},
                #{item.huobdh, jdbcType=VARCHAR},
                #{item.htngje, jdbcType=DECIMAL},
                #{item.jiejje, jdbcType=DECIMAL},
                #{item.yfngje, jdbcType=DECIMAL},
                #{item.djkfje, jdbcType=DECIMAL},
                #{item.kfngje, jdbcType=DECIMAL},
                #{item.jiluzt, jdbcType=VARCHAR},
                #{item.dkzhzt, jdbcType=VARCHAR},
                #{item.yngjzt, jdbcType=VARCHAR},
                #{item.daikxt, jdbcType=VARCHAR},
                #{item.qixian, jdbcType=VARCHAR},
                #{item.xiohrq, jdbcType=VARCHAR},
                #{item.weihrq, jdbcType=VARCHAR},
                #{item.dzhibj, jdbcType=DECIMAL},
                #{item.fajnsr, jdbcType=DECIMAL},
                #{item.yinsfj, jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <!-- 清空贷款账户主历史表所有数据 -->
    <delete id="deleteAll" parameterType="java.lang.String">
        TRUNCATE TABLE lb_h_core_adkzh
    </delete>

    <!-- 根据数据日期删除贷款账户主历史表记录 -->
    <delete id="deleteByDataDate" parameterType="java.lang.String">
        DELETE FROM lb_h_core_adkzh WHERE data_date = #{dataDate}
    </delete>
</mapper>