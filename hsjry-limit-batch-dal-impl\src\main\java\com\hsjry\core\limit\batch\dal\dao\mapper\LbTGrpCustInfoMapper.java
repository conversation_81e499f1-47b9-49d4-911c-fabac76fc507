package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTGrpCustInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-集团客户信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTGrpCustInfoMapper extends CommonMapper<LbTGrpCustInfoDo> {
    
    /**
     * 清空额度中心-中间表-集团客户信息表数据
     *
     * @return 影响行数
     */
    int truncateTable();

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @return 影响行数
     */
    int insertFromSource();

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    int insertFromSourceByUserIdList(List<String> userIdList);
}