package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBcdhpDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 核心系统-落地表-产品定义表（银行承兑汇票管理）mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSCoreBcdhpMapper extends CommonMapper<LbSCoreBcdhpDo> {
    
    /**
     * 批量插入银行承兑汇票数据
     *
     * @param lbSCoreBcdhpList 批量数据
     * @return int
     */
    int insertList(@Param("list") List<LbSCoreBcdhpDo> lbSCoreBcdhpList);
    
    /**
     * 清空银行承兑汇票表所有数据
     *
     * @return int
     */
    int deleteAll();
}