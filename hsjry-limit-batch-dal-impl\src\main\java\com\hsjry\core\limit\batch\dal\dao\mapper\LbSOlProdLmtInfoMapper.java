package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlProdLmtInfoQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 网贷系统-落地表-产品额度信息（记录客户产品额度信息）mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlProdLmtInfoMapper extends CommonMapper<LbSOlProdLmtInfoDo> {

    /**
     * 批量插入网贷系统-落地表-产品额度信息
     *
     * @param list 批量数据
     * @return int
     */
    int insertList(List<LbSOlProdLmtInfoDo> list);

    /**
     * 清空网贷系统-落地表-产品额度信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据条件查询数据量
     * 
     * @param example 条件
     * @return long
     */
    long countByExample(LbSOlProdLmtInfoExample example);

}