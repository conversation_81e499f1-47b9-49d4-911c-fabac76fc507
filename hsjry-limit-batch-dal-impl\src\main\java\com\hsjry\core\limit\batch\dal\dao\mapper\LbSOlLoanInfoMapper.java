package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlLoanInfoQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 网贷系统-落地表-借据信息（记录客户借款信息）mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlLoanInfoMapper extends CommonMapper<LbSOlLoanInfoDo> {

    /**
     * 批量插入借据信息
     *
     * @param lbSOlLoanInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbSOlLoanInfoDo> lbSOlLoanInfoList);

    /**
     * 清空借据信息表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbSOlLoanInfoDo>
     */
    List<LbSOlLoanInfoDo> selectShardList(@Param("query") LbSOlLoanInfoQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbSOlLoanInfoDo
     */
    LbSOlLoanInfoDo selectFirstOne(@Param("query") LbSOlLoanInfoQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(@Param("query") LbSOlLoanInfoQuery query);

    /**
     * 根据条件查询数据量
     * 
     * @param example 条件
     * @return long
     */
    long countByExample(LbSOlLoanInfoExample example);
}