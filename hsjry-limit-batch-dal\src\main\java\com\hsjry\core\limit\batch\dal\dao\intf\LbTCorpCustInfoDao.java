package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTCorpCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCorpCustInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-对公客户信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTCorpCustInfoDao extends IBaseDao<LbTCorpCustInfoDo> {
    /**
     * 分页查询额度中心-中间表-对公客户信息
     *
     * @param lbTCorpCustInfoQuery 条件
     * @return PageInfo<LbTCorpCustInfoDo>
     */
    PageInfo<LbTCorpCustInfoDo> selectPage(LbTCorpCustInfoQuery lbTCorpCustInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-对公客户信息
     *
     * @param custNo
     * @return
     */
    LbTCorpCustInfoDo selectByKey(String custNo);

    /**
     * 根据key删除额度中心-中间表-对公客户信息
     *
     * @param custNo
     * @return
     */
    int deleteByKey(String custNo);

    /**
     * 查询额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfoQuery 条件
     * @return List<LbTCorpCustInfoDo>
     */
    List<LbTCorpCustInfoDo> selectByExample(LbTCorpCustInfoQuery lbTCorpCustInfoQuery);

    /**
     * 新增额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo 条件
     * @return int>
     */
    int insertBySelective(LbTCorpCustInfoDo lbTCorpCustInfo);

    /**
     * 修改额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo
     * @return
     */
    int updateBySelective(LbTCorpCustInfoDo lbTCorpCustInfo);

    /**
     * 修改额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo
     * @param lbTCorpCustInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTCorpCustInfoDo lbTCorpCustInfo, LbTCorpCustInfoQuery lbTCorpCustInfoQuery);

    /**
     * 清空额度中心-中间表-对公客户信息表数据
     *
     * @return 影响行数
     */
    int truncateTable();

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入对公客户信息数据
     *
     * @return 影响行数
     */
    int insertFromSource();

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入对公客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    int insertFromSource(List<String> userIdList);
}
