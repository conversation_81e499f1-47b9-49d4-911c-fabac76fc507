<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbSCoreBcdhpMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBcdhpDo">
        <result property="wjdkfl" column="wjdkfl" jdbcType="VARCHAR"/> <!-- 五级贷款分类 -->
        <result property="edceng" column="edceng" jdbcType="VARCHAR"/> <!-- 额度层次 -->
        <result property="ycqffs" column="ycqffs" jdbcType="VARCHAR"/> <!-- 银承汇票签发方式 -->
        <result property="dnknbz" column="dnknbz" jdbcType="VARCHAR"/> <!-- 垫款标志 -->
        <result property="jiejuh" column="jiejuh" jdbcType="VARCHAR"/> <!-- 垫款借据编号 -->
        <result property="zijnqx" column="zijnqx" jdbcType="VARCHAR"/> <!-- 资金去向 -->
        <result property="dczrzh" column="dczrzh" jdbcType="VARCHAR"/> <!-- 资金转入账号 -->
        <result property="jioyrq" column="jioyrq" jdbcType="VARCHAR"/> <!-- 交易日期 -->
        <result property="daoqrq" column="daoqrq" jdbcType="VARCHAR"/> <!-- 到期日期 -->
        <result property="qnfarq" column="qnfarq" jdbcType="VARCHAR"/> <!-- 签发日期 -->
        <result property="cduirq" column="cduirq" jdbcType="VARCHAR"/> <!-- 承兑日期 -->
        <result property="ruzhrq" column="ruzhrq" jdbcType="VARCHAR"/> <!-- 备款日期 -->
        <result property="sxiorq" column="sxiorq" jdbcType="VARCHAR"/> <!-- 未用退回日期 -->
        <result property="wjflrq" column="wjflrq" jdbcType="VARCHAR"/> <!-- 五级分类日期 -->
        <result property="gsriqi" column="gsriqi" jdbcType="VARCHAR"/> <!-- 挂失日期 -->
        <result property="jgriqi" column="jgriqi" jdbcType="VARCHAR"/> <!-- 解挂日期 -->
        <result property="skzhao" column="skzhao" jdbcType="VARCHAR"/> <!-- 持票人账号 -->
        <result property="chipmc" column="chipmc" jdbcType="VARCHAR"/> <!-- 持票人名称 -->
        <result property="shfobz" column="shfobz" jdbcType="VARCHAR"/> <!-- 是否全额保证金 -->
        <result property="ychpzt" column="ychpzt" jdbcType="VARCHAR"/> <!-- 承兑汇票状态 -->
        <result property="piojzt" column="piojzt" jdbcType="VARCHAR"/> <!-- 票据状态 -->
        <result property="mxxhao" column="mxxhao" jdbcType="DECIMAL"/> <!-- 明细序号 -->
        <result property="remark" column="remark" jdbcType="VARCHAR"/> <!-- 备注 -->
        <result property="kaihjg" column="kaihjg" jdbcType="VARCHAR"/> <!-- 开户机构 -->
        <result property="kaihgy" column="kaihgy" jdbcType="VARCHAR"/> <!-- 开户柜员 -->
        <result property="weihgy" column="weihgy" jdbcType="VARCHAR"/> <!-- 维护柜员 -->
        <result property="xiohgy" column="xiohgy" jdbcType="VARCHAR"/> <!-- 销户柜员 -->
        <result property="weihjg" column="weihjg" jdbcType="VARCHAR"/> <!-- 维护机构 -->
        <result property="weihrq" column="weihrq" jdbcType="VARCHAR"/> <!-- 维护日期 -->
        <result property="xiohrq" column="xiohrq" jdbcType="VARCHAR"/> <!-- 销户日期 -->
        <result property="weihsj" column="weihsj" jdbcType="VARCHAR"/> <!-- 维护时间 -->
        <result property="shjnch" column="shjnch" jdbcType="VARCHAR"/> <!-- 时间戳 -->
        <result property="jiluzt" column="jiluzt" jdbcType="VARCHAR"/> <!-- 记录状态 -->
        <result property="chprqc" column="chprqc" jdbcType="VARCHAR"/> <!-- 出票人全称 -->
        <result property="cdxybh" column="cdxybh" jdbcType="VARCHAR"/> <!-- 银承协议编号 -->
        <result property="bccppc" column="bccppc" jdbcType="DECIMAL"/> <!-- 本次出票批次 -->
        <result property="chupbh" column="chupbh" jdbcType="VARCHAR"/> <!-- 本次出票编号 -->
        <result property="yngyjg" column="yngyjg" jdbcType="VARCHAR"/> <!-- 签发机构号 -->
        <result property="zhngjg" column="zhngjg" jdbcType="VARCHAR"/> <!-- 申请机构号 -->
        <result property="htngbh" column="htngbh" jdbcType="VARCHAR"/> <!-- 银承合同编号 -->
        <result property="zongje" column="zongje" jdbcType="DECIMAL"/> <!-- 协议总票面金额 -->
        <result property="huobdh" column="huobdh" jdbcType="VARCHAR"/> <!-- 币种 -->
        <result property="piomje" column="piomje" jdbcType="DECIMAL"/> <!-- 票面金额 -->
        <result property="huipje" column="huipje" jdbcType="DECIMAL"/> <!-- 汇票金额 -->
        <result property="beikje" column="beikje" jdbcType="DECIMAL"/> <!-- 备款金额 -->
        <result property="zhdkje" column="zhdkje" jdbcType="DECIMAL"/> <!-- 垫款金额 -->
        <result property="kehhao" column="kehhao" jdbcType="VARCHAR"/> <!-- 出票人客户号 -->
        <result property="chprzh" column="chprzh" jdbcType="VARCHAR"/> <!-- 出票人帐号 -->
        <result property="faredm" column="faredm" jdbcType="VARCHAR"/> <!-- 法人代码 -->
        <result property="skrkhh" column="skrkhh" jdbcType="VARCHAR"/> <!-- 收款人客户号 -->
        <result property="shkrzh" column="shkrzh" jdbcType="VARCHAR"/> <!-- 收款人帐号 -->
        <result property="shkrxm" column="shkrxm" jdbcType="VARCHAR"/> <!-- 收款人户名 -->
        <result property="qfhlhh" column="qfhlhh" jdbcType="VARCHAR"/> <!-- 签发行联行行号 -->
        <result property="qfhlhm" column="qfhlhm" jdbcType="VARCHAR"/> <!-- 签发行联行行名 -->
        <result property="skhhao" column="skhhao" jdbcType="VARCHAR"/> <!-- 收款行行号 -->
        <result property="shkhhm" column="shkhhm" jdbcType="VARCHAR"/> <!-- 收款行行名 -->
        <result property="kaihhh" column="kaihhh" jdbcType="VARCHAR"/> <!-- 持票人开户行行号 -->
        <result property="kaihhm" column="kaihhm" jdbcType="VARCHAR"/> <!-- 持票人开户行行名 -->
        <result property="beizxx" column="beizxx" jdbcType="VARCHAR"/> <!-- 签发行联行地址 -->
        <result property="pngzzl" column="pngzzl" jdbcType="VARCHAR"/> <!-- 凭证种类 -->
        <result property="pjlugz" column="pjlugz" jdbcType="VARCHAR"/> <!-- 票据轮冠字 -->
        <result property="piojhm" column="piojhm" jdbcType="VARCHAR"/> <!-- 票据号码 -->
        <result property="sfdzpj" column="sfdzpj" jdbcType="VARCHAR"/> <!-- 是否电子票据 -->
    </resultMap>
    <sql id="Base_Column_List">
        wjdkfl
        , edceng
        , ycqffs
        , dnknbz
        , jiejuh
        , zijnqx
        , dczrzh
        , jioyrq
        , daoqrq
        , qnfarq
        , cduirq
        , ruzhrq
        , sxiorq
        , wjflrq
        , gsriqi
        , jgriqi
        , skzhao
        , chipmc
        , shfobz
        , ychpzt
        , piojzt
        , mxxhao
        , remark
        , kaihjg
        , kaihgy
        , weihgy
        , xiohgy
        , weihjg
        , weihrq
        , xiohrq
        , weihsj
        , shjnch
        , jiluzt
        , chprqc
        , cdxybh
        , bccppc
        , chupbh
        , yngyjg
        , zhngjg
        , htngbh
        , zongje
        , huobdh
        , piomje
        , huipje
        , beikje
        , zhdkje
        , kehhao
        , chprzh
        , faredm
        , skrkhh
        , shkrzh
        , shkrxm
        , qfhlhh
        , qfhlhm
        , skhhao
        , shkhhm
        , kaihhh
        , kaihhm
        , beizxx
        , pngzzl
        , pjlugz
        , piojhm
        , sfdzpj
    </sql>
    
    <!-- 批量插入银行承兑汇票数据 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO LB_S_CORE_BCDHP (
            FAREDM, CDXYBH, BCCPPC, CHUPBH, YNGYJG, ZHNGJG, HTNGBH, ZONGJE, PNGZZL, PJLUGZ,
            PIOJHM, HUOBDH, PIOMJE, HUIPJE, BEIKJE, QFHLHH, QFHLHM, KEHHAO, CHPRZH, CHPRQC,
            SKRKHH, SHKRZH, SHKRXM, SKHHAO, SHKHHM, SHFOBZ, EDCENG, YCQFFS, JIOYRQ, DAOQRQ,
            QNFARQ, CDUIRQ, RUZHRQ, DNKNBZ, JIEJUH, ZHDKJE, ZIJNQX, DCZRZH, SKZHAO, CHIPMC,
            KAIHHH, KAIHHM, SXIORQ, WJFLRQ, WJDKFL, YCHPZT, PIOJZT, SFDZPJ, MXXHAO, GSRIQI,
            JGRIQI, REMARK, BEIZXX, KAIHJG, KAIHGY, WEIHGY, WEIHRQ, XIOHGY, XIOHRQ, WEIHJG,
            WEIHSJ, SHJNCH, JILUZT
            ) VALUES (
            #{item.faredm, jdbcType=VARCHAR},
            #{item.cdxybh, jdbcType=VARCHAR},
            #{item.bccppc, jdbcType=DECIMAL},
            #{item.chupbh, jdbcType=VARCHAR},
            #{item.yngyjg, jdbcType=VARCHAR},
            #{item.zhngjg, jdbcType=VARCHAR},
            #{item.htngbh, jdbcType=VARCHAR},
            #{item.zongje, jdbcType=DECIMAL},
            #{item.pngzzl, jdbcType=VARCHAR},
            #{item.pjlugz, jdbcType=VARCHAR},
            #{item.piojhm, jdbcType=VARCHAR},
            #{item.huobdh, jdbcType=VARCHAR},
            #{item.piomje, jdbcType=DECIMAL},
            #{item.huipje, jdbcType=DECIMAL},
            #{item.beikje, jdbcType=DECIMAL},
            #{item.qfhlhh, jdbcType=VARCHAR},
            #{item.qfhlhm, jdbcType=VARCHAR},
            #{item.kehhao, jdbcType=VARCHAR},
            #{item.chprzh, jdbcType=VARCHAR},
            #{item.chprqc, jdbcType=VARCHAR},
            #{item.skrkhh, jdbcType=VARCHAR},
            #{item.shkrzh, jdbcType=VARCHAR},
            #{item.shkrxm, jdbcType=VARCHAR},
            #{item.skhhao, jdbcType=VARCHAR},
            #{item.shkhhm, jdbcType=VARCHAR},
            #{item.shfobz, jdbcType=VARCHAR},
            #{item.edceng, jdbcType=VARCHAR},
            #{item.ycqffs, jdbcType=VARCHAR},
            #{item.jioyrq, jdbcType=VARCHAR},
            #{item.daoqrq, jdbcType=VARCHAR},
            #{item.qnfarq, jdbcType=VARCHAR},
            #{item.cduirq, jdbcType=VARCHAR},
            #{item.ruzhrq, jdbcType=VARCHAR},
            #{item.dnknbz, jdbcType=VARCHAR},
            #{item.jiejuh, jdbcType=VARCHAR},
            #{item.zhdkje, jdbcType=DECIMAL},
            #{item.zijnqx, jdbcType=VARCHAR},
            #{item.dczrzh, jdbcType=VARCHAR},
            #{item.skzhao, jdbcType=VARCHAR},
            #{item.chipmc, jdbcType=VARCHAR},
            #{item.kaihhh, jdbcType=VARCHAR},
            #{item.kaihhm, jdbcType=VARCHAR},
            #{item.sxiorq, jdbcType=VARCHAR},
            #{item.wjflrq, jdbcType=VARCHAR},
            #{item.wjdkfl, jdbcType=VARCHAR},
            #{item.ychpzt, jdbcType=VARCHAR},
            #{item.piojzt, jdbcType=VARCHAR},
            #{item.sfdzpj, jdbcType=VARCHAR},
            #{item.mxxhao, jdbcType=DECIMAL},
            #{item.gsriqi, jdbcType=VARCHAR},
            #{item.jgriqi, jdbcType=VARCHAR},
            #{item.remark, jdbcType=VARCHAR},
            #{item.beizxx, jdbcType=VARCHAR},
            #{item.kaihjg, jdbcType=VARCHAR},
            #{item.kaihgy, jdbcType=VARCHAR},
            #{item.weihgy, jdbcType=VARCHAR},
            #{item.weihrq, jdbcType=VARCHAR},
            #{item.xiohgy, jdbcType=VARCHAR},
            #{item.xiohrq, jdbcType=VARCHAR},
            #{item.weihjg, jdbcType=VARCHAR},
            #{item.weihsj, jdbcType=VARCHAR},
            #{item.shjnch, jdbcType=VARCHAR},
            #{item.jiluzt, jdbcType=VARCHAR}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>
    
    <!-- 清空所有数据 -->
    <delete id="deleteAll">
        DELETE FROM LB_S_CORE_BCDHP
    </delete>
</mapper>