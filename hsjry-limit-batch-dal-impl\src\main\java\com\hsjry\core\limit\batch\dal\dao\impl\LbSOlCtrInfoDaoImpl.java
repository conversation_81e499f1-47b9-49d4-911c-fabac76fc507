package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSOlCtrInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlCtrInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-历史表-合同信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbSOlCtrInfoDaoImpl extends AbstractBaseDaoImpl<LbSOlCtrInfoDo, LbSOlCtrInfoMapper>
    implements LbSOlCtrInfoDao {
    /**
     * 分页查询
     *
     * @param lbSOlCtrInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSOlCtrInfoDo> selectPage(LbSOlCtrInfoQuery lbSOlCtrInfo, PageParam pageParam) {
        LbSOlCtrInfoExample example = buildExample(lbSOlCtrInfo);
        return PageHelper.<LbSOlCtrInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-历史表-合同信息
     *
     * @param contractId
     * @return
     */
    @Override
    public LbSOlCtrInfoDo selectByKey(String contractId) {
        LbSOlCtrInfoKeyDo lbSOlCtrInfoKeyDo = new LbSOlCtrInfoKeyDo();
        lbSOlCtrInfoKeyDo.setContractId(contractId);
        lbSOlCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbSOlCtrInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-历史表-合同信息
     *
     * @param contractId
     * @return
     */
    @Override
    public int deleteByKey(String contractId) {
        LbSOlCtrInfoKeyDo lbSOlCtrInfoKeyDo = new LbSOlCtrInfoKeyDo();
        lbSOlCtrInfoKeyDo.setContractId(contractId);
        lbSOlCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbSOlCtrInfoKeyDo);
    }

    /**
     * 查询网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo 条件
     * @return List<LbSOlCtrInfoDo>
     */
    @Override
    public List<LbSOlCtrInfoDo> selectByExample(LbSOlCtrInfoQuery lbSOlCtrInfo) {
        return getMapper().selectByExample(buildExample(lbSOlCtrInfo));
    }

    /**
     * 新增网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSOlCtrInfoDo lbSOlCtrInfo) {
        if (lbSOlCtrInfo == null) {
            return -1;
        }

        lbSOlCtrInfo.setCreateTime(BusinessDateUtil.getDate());
        lbSOlCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbSOlCtrInfo);
    }

    /**
     * 修改网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo
     * @return
     */
    @Override
    public int updateBySelective(LbSOlCtrInfoDo lbSOlCtrInfo) {
        if (lbSOlCtrInfo == null) {
            return -1;
        }
        lbSOlCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbSOlCtrInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbSOlCtrInfoDo lbSOlCtrInfo, LbSOlCtrInfoQuery lbSOlCtrInfoQuery) {
        lbSOlCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbSOlCtrInfo, buildExample(lbSOlCtrInfoQuery));
    }

    @Override
    public int deleteAll() {
        return getMapper().deleteAll() ;
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSOlCtrInfoDo selectFirstOne(LbSOlCtrInfoQuery query) {
        LbSOlCtrInfoExample example = buildExample(query);
        example.setOrderByClause("contract_id ASC, tenant_id ASC");
        List<LbSOlCtrInfoDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSOlCtrInfoQuery query) {
        LbSOlCtrInfoExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

    /**
     * 构建网贷系统-历史表-合同信息Example信息
     *
     * @param lbSOlCtrInfo
     * @return
     */
    public LbSOlCtrInfoExample buildExample(LbSOlCtrInfoQuery lbSOlCtrInfo) {
        LbSOlCtrInfoExample example = new LbSOlCtrInfoExample();
        LbSOlCtrInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbSOlCtrInfo != null) {
            //添加查询条件
            if (null != lbSOlCtrInfo.getEffectiveStartTimeBegin()) {
                criteria.andEffectiveStartTimeGreaterThanOrEqualTo(lbSOlCtrInfo.getEffectiveStartTimeBegin());
            }
            if (null != lbSOlCtrInfo.getEffectiveStartTimeEnd()) {
                criteria.andEffectiveStartTimeLessThanOrEqualTo(lbSOlCtrInfo.getEffectiveStartTimeEnd());
            }
            if (null != lbSOlCtrInfo.getContractAmount()) {
                criteria.andContractAmountEqualTo(lbSOlCtrInfo.getContractAmount());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getLimitMode())) {
                criteria.andLimitModeEqualTo(lbSOlCtrInfo.getLimitMode());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getLoanApplyId())) {
                criteria.andLoanApplyIdEqualTo(lbSOlCtrInfo.getLoanApplyId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getLoanInvoiceId())) {
                criteria.andLoanInvoiceIdEqualTo(lbSOlCtrInfo.getLoanInvoiceId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbSOlCtrInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbSOlCtrInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbSOlCtrInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractMode())) {
                criteria.andContractModeEqualTo(lbSOlCtrInfo.getContractMode());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getOnlineFlag())) {
                criteria.andOnlineFlagEqualTo(lbSOlCtrInfo.getOnlineFlag());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getThirdpartId())) {
                criteria.andThirdpartIdEqualTo(lbSOlCtrInfo.getThirdpartId());
            }
            if (null != lbSOlCtrInfo.getEffectiveEndTimeBegin()) {
                criteria.andEffectiveEndTimeGreaterThanOrEqualTo(lbSOlCtrInfo.getEffectiveEndTimeBegin());
            }
            if (null != lbSOlCtrInfo.getEffectiveEndTimeEnd()) {
                criteria.andEffectiveEndTimeLessThanOrEqualTo(lbSOlCtrInfo.getEffectiveEndTimeEnd());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractId())) {
                criteria.andContractIdEqualTo(lbSOlCtrInfo.getContractId());
            }
            if (null != lbSOlCtrInfo.getSignTimeBegin()) {
                criteria.andSignTimeGreaterThanOrEqualTo(lbSOlCtrInfo.getSignTimeBegin());
            }
            if (null != lbSOlCtrInfo.getSignTimeEnd()) {
                criteria.andSignTimeLessThanOrEqualTo(lbSOlCtrInfo.getSignTimeEnd());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getCertificateNo())) {
                criteria.andCertificateNoEqualTo(lbSOlCtrInfo.getCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getCertificateType())) {
                criteria.andCertificateTypeEqualTo(lbSOlCtrInfo.getCertificateType());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getUserPhone())) {
                criteria.andUserPhoneEqualTo(lbSOlCtrInfo.getUserPhone());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getUserName())) {
                criteria.andUserNameEqualTo(lbSOlCtrInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getThirdpartContractId())) {
                criteria.andThirdpartContractIdEqualTo(lbSOlCtrInfo.getThirdpartContractId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractStatus())) {
                criteria.andContractStatusEqualTo(lbSOlCtrInfo.getContractStatus());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getProductId())) {
                criteria.andProductIdEqualTo(lbSOlCtrInfo.getProductId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getRelationIdType())) {
                criteria.andRelationIdTypeEqualTo(lbSOlCtrInfo.getRelationIdType());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbSOlCtrInfo.getRelationId());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractFileUrl())) {
                criteria.andContractFileUrlEqualTo(lbSOlCtrInfo.getContractFileUrl());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractType())) {
                criteria.andContractTypeEqualTo(lbSOlCtrInfo.getContractType());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getContractName())) {
                criteria.andContractNameEqualTo(lbSOlCtrInfo.getContractName());
            }
            if (StringUtil.isNotEmpty(lbSOlCtrInfo.getUserId())) {
                criteria.andUserIdEqualTo(lbSOlCtrInfo.getUserId());
            }
        }
        buildExampleExt(lbSOlCtrInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-历史表-合同信息ExampleExt方法
     *
     * @param lbSOlCtrInfo
     * @return
     */
    public void buildExampleExt(LbSOlCtrInfoQuery lbSOlCtrInfo, LbSOlCtrInfoExample.Criteria criteria) {

        //自定义实现
    }

}
