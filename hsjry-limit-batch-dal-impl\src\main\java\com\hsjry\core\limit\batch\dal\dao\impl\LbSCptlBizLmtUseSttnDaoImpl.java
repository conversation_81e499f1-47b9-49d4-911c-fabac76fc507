package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbSCptlBizLmtUseSttnDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSCptlBizLmtUseSttnMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCptlBizLmtUseSttnQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金日终业务额度使用情况同步表（记录客户额度使用情况）数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbSCptlBizLmtUseSttnDaoImpl extends AbstractBaseDaoImpl<LbSCptlBizLmtUseSttnDo, LbSCptlBizLmtUseSttnMapper>
    implements LbSCptlBizLmtUseSttnDao {
    /**
     * 分页查询
     *
     * @param lbSCptlBizLmtUseSttn 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSCptlBizLmtUseSttnDo> selectPage(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttn,
        PageParam pageParam) {
        LbSCptlBizLmtUseSttnExample example = buildExample(lbSCptlBizLmtUseSttn);
        return PageHelper.<LbSCptlBizLmtUseSttnDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @return
     */
    @Override
    public LbSCptlBizLmtUseSttnDo selectByKey(String userId) {
        LbSCptlBizLmtUseSttnKeyDo lbSCptlBizLmtUseSttnKeyDo = new LbSCptlBizLmtUseSttnKeyDo();
        lbSCptlBizLmtUseSttnKeyDo.setUserId(userId);
        return getMapper().selectByPrimaryKey(lbSCptlBizLmtUseSttnKeyDo);
    }

    /**
     * 根据key删除资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @return
     */
    @Override
    public int deleteByKey(String userId) {
        LbSCptlBizLmtUseSttnKeyDo lbSCptlBizLmtUseSttnKeyDo = new LbSCptlBizLmtUseSttnKeyDo();
        lbSCptlBizLmtUseSttnKeyDo.setUserId(userId);
        return getMapper().deleteByPrimaryKey(lbSCptlBizLmtUseSttnKeyDo);
    }

    /**
     * 查询资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn 条件
     * @return List<LbSCptlBizLmtUseSttnDo>
     */
    @Override
    public List<LbSCptlBizLmtUseSttnDo> selectByExample(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttn) {
        return getMapper().selectByExample(buildExample(lbSCptlBizLmtUseSttn));
    }

    /**
     * 新增资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn) {
        if (lbSCptlBizLmtUseSttn == null) {
            return -1;
        }

        return getMapper().insertSelective(lbSCptlBizLmtUseSttn);
    }

    /**
     * 修改资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn
     * @return
     */
    @Override
    public int updateBySelective(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn) {
        if (lbSCptlBizLmtUseSttn == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbSCptlBizLmtUseSttn);
    }

    @Override
    public int updateBySelectiveByExample(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn,
        LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttnQuery) {
        return getMapper().updateByExampleSelective(lbSCptlBizLmtUseSttn, buildExample(lbSCptlBizLmtUseSttnQuery));
    }

    /**
     * 构建资金日终业务额度使用情况同步表（记录客户额度使用情况）Example信息
     *
     * @param lbSCptlBizLmtUseSttn
     * @return
     */
    public LbSCptlBizLmtUseSttnExample buildExample(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttn) {
        LbSCptlBizLmtUseSttnExample example = new LbSCptlBizLmtUseSttnExample();
        LbSCptlBizLmtUseSttnExample.Criteria criteria = example.createCriteria();
        if (lbSCptlBizLmtUseSttn != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getUserType())) {
                criteria.andUserTypeEqualTo(lbSCptlBizLmtUseSttn.getUserType());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getUserId())) {
                criteria.andUserIdEqualTo(lbSCptlBizLmtUseSttn.getUserId());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getUserName())) {
                criteria.andUserNameEqualTo(lbSCptlBizLmtUseSttn.getUserName());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getUserCertificateKind())) {
                criteria.andUserCertificateKindEqualTo(lbSCptlBizLmtUseSttn.getUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getUserCertificateNo())) {
                criteria.andUserCertificateNoEqualTo(lbSCptlBizLmtUseSttn.getUserCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getProductId())) {
                criteria.andProductIdEqualTo(lbSCptlBizLmtUseSttn.getProductId());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getProductName())) {
                criteria.andProductNameEqualTo(lbSCptlBizLmtUseSttn.getProductName());
            }
            if (StringUtil.isNotEmpty(lbSCptlBizLmtUseSttn.getCustLimitTypeName())) {
                criteria.andCustLimitTypeNameEqualTo(lbSCptlBizLmtUseSttn.getCustLimitTypeName());
            }
            if (null != lbSCptlBizLmtUseSttn.getUsedAmount()) {
                criteria.andUsedAmountEqualTo(lbSCptlBizLmtUseSttn.getUsedAmount());
            }
        }
        buildExampleExt(lbSCptlBizLmtUseSttn, criteria);
        return example;
    }

    /**
     * 构建资金日终业务额度使用情况同步表（记录客户额度使用情况）ExampleExt方法
     *
     * @param lbSCptlBizLmtUseSttn
     * @return
     */
    public void buildExampleExt(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttn,
        LbSCptlBizLmtUseSttnExample.Criteria criteria) {

        //自定义实现
    }

    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    @Override
    public int batchInsert(List<LbSCptlBizLmtUseSttnDo> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(list);
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据userId主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSCptlBizLmtUseSttnDo selectFirstOne(LbSCptlBizLmtUseSttnQuery query) {
        LbSCptlBizLmtUseSttnExample example = buildExample(query);
        example.setOrderByClause("user_id ASC");
        List<LbSCptlBizLmtUseSttnDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据userId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含userId范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSCptlBizLmtUseSttnQuery query) {
        LbSCptlBizLmtUseSttnExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

}
