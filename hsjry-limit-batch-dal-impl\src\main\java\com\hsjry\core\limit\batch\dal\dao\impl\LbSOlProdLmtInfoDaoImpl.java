package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSOlProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-落地表-产品额度信息（记录客户产品额度信息）数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbSOlProdLmtInfoDaoImpl extends AbstractBaseDaoImpl<LbSOlProdLmtInfoDo, LbSOlProdLmtInfoMapper>
    implements LbSOlProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbSOlProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSOlProdLmtInfoDo> selectPage(LbSOlProdLmtInfoQuery lbSOlProdLmtInfo, PageParam pageParam) {
        LbSOlProdLmtInfoExample example = buildExample(lbSOlProdLmtInfo);
        return PageHelper.<LbSOlProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-落地表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @return
     */
    @Override
    public LbSOlProdLmtInfoDo selectByKey(String creditLimitId) {
        LbSOlProdLmtInfoKeyDo lbSOlProdLmtInfoKeyDo = new LbSOlProdLmtInfoKeyDo();
        lbSOlProdLmtInfoKeyDo.setCreditLimitId(creditLimitId);
        lbSOlProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbSOlProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-落地表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @return
     */
    @Override
    public int deleteByKey(String creditLimitId) {
        LbSOlProdLmtInfoKeyDo lbSOlProdLmtInfoKeyDo = new LbSOlProdLmtInfoKeyDo();
        lbSOlProdLmtInfoKeyDo.setCreditLimitId(creditLimitId);
        lbSOlProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbSOlProdLmtInfoKeyDo);
    }

    /**
     * 查询网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo 条件
     * @return List<LbSOlProdLmtInfoDo>
     */
    @Override
    public List<LbSOlProdLmtInfoDo> selectByExample(LbSOlProdLmtInfoQuery lbSOlProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbSOlProdLmtInfo));
    }

    /**
     * 新增网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSOlProdLmtInfoDo lbSOlProdLmtInfo) {
        if (lbSOlProdLmtInfo == null) {
            return -1;
        }

        lbSOlProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbSOlProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbSOlProdLmtInfo);
    }

    /**
     * 修改网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbSOlProdLmtInfoDo lbSOlProdLmtInfo) {
        if (lbSOlProdLmtInfo == null) {
            return -1;
        }
        lbSOlProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbSOlProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbSOlProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbSOlProdLmtInfoDo lbSOlProdLmtInfo,
        LbSOlProdLmtInfoQuery lbSOlProdLmtInfoQuery) {
        lbSOlProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbSOlProdLmtInfo, buildExample(lbSOlProdLmtInfoQuery));
    }

    /**
     * 构建网贷系统-落地表-产品额度信息（记录客户产品额度信息）Example信息
     *
     * @param lbSOlProdLmtInfo
     * @return
     */
    public LbSOlProdLmtInfoExample buildExample(LbSOlProdLmtInfoQuery lbSOlProdLmtInfo) {
        LbSOlProdLmtInfoExample example = new LbSOlProdLmtInfoExample();
        LbSOlProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbSOlProdLmtInfo != null) {
            //添加查询条件
            if (null != lbSOlProdLmtInfo.getFrozenAmount()) {
                criteria.andFrozenAmountEqualTo(lbSOlProdLmtInfo.getFrozenAmount());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbSOlProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbSOlProdLmtInfo.getOperatorId());
            }
            if (null != lbSOlProdLmtInfo.getEffectiveEndTimeBegin()) {
                criteria.andEffectiveEndTimeGreaterThanOrEqualTo(lbSOlProdLmtInfo.getEffectiveEndTimeBegin());
            }
            if (null != lbSOlProdLmtInfo.getEffectiveEndTimeEnd()) {
                criteria.andEffectiveEndTimeLessThanOrEqualTo(lbSOlProdLmtInfo.getEffectiveEndTimeEnd());
            }
            if (null != lbSOlProdLmtInfo.getEffectiveStartTimeBegin()) {
                criteria.andEffectiveStartTimeGreaterThanOrEqualTo(lbSOlProdLmtInfo.getEffectiveStartTimeBegin());
            }
            if (null != lbSOlProdLmtInfo.getEffectiveStartTimeEnd()) {
                criteria.andEffectiveStartTimeLessThanOrEqualTo(lbSOlProdLmtInfo.getEffectiveStartTimeEnd());
            }
            if (null != lbSOlProdLmtInfo.getLoanTimesLimit()) {
                criteria.andLoanTimesLimitEqualTo(lbSOlProdLmtInfo.getLoanTimesLimit());
            }
            if (null != lbSOlProdLmtInfo.getUseLoanTimes()) {
                criteria.andUseLoanTimesEqualTo(lbSOlProdLmtInfo.getUseLoanTimes());
            }
            if (null != lbSOlProdLmtInfo.getUsedAmount()) {
                criteria.andUsedAmountEqualTo(lbSOlProdLmtInfo.getUsedAmount());
            }
            if (null != lbSOlProdLmtInfo.getUsingAmount()) {
                criteria.andUsingAmountEqualTo(lbSOlProdLmtInfo.getUsingAmount());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getCreditLimitId())) {
                criteria.andCreditLimitIdEqualTo(lbSOlProdLmtInfo.getCreditLimitId());
            }
            if (null != lbSOlProdLmtInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbSOlProdLmtInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getStatus())) {
                criteria.andStatusEqualTo(lbSOlProdLmtInfo.getStatus());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getCreditType())) {
                criteria.andCreditTypeEqualTo(lbSOlProdLmtInfo.getCreditType());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getProductName())) {
                criteria.andProductNameEqualTo(lbSOlProdLmtInfo.getProductName());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getProductId())) {
                criteria.andProductIdEqualTo(lbSOlProdLmtInfo.getProductId());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getUserMobile())) {
                criteria.andUserMobileEqualTo(lbSOlProdLmtInfo.getUserMobile());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getCertificateType())) {
                criteria.andCertificateTypeEqualTo(lbSOlProdLmtInfo.getCertificateType());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getCertificateNo())) {
                criteria.andCertificateNoEqualTo(lbSOlProdLmtInfo.getCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getUserType())) {
                criteria.andUserTypeEqualTo(lbSOlProdLmtInfo.getUserType());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getUserName())) {
                criteria.andUserNameEqualTo(lbSOlProdLmtInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lbSOlProdLmtInfo.getUserId())) {
                criteria.andUserIdEqualTo(lbSOlProdLmtInfo.getUserId());
            }
        }
        buildExampleExt(lbSOlProdLmtInfo, criteria);
        return example;
    }



    /**
     * 构建网贷系统-落地表-产品额度信息（记录客户产品额度信息）ExampleExt方法
     *
     * @param lbSOlProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbSOlProdLmtInfoQuery lbSOlProdLmtInfo, LbSOlProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入网贷系统产品额度信息表-落地信息
     *
     * @param lbSOlProdLmtInfoList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbSOlProdLmtInfoDo> lbSOlProdLmtInfoList) {
        if (lbSOlProdLmtInfoList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbSOlProdLmtInfoList);
    }

    /**
     * 清空网贷系统产品额度信息表-落地所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSOlProdLmtInfoDo selectFirstOne(LbSOlProdLmtInfoQuery query) {
        LbSOlProdLmtInfoExample example = buildExample(query);
        example.setOrderByClause("credit_limit_id ASC, tenant_id ASC");
        List<LbSOlProdLmtInfoDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSOlProdLmtInfoQuery query) {
        LbSOlProdLmtInfoExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

}
