package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCcsAcctDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCcsAcctMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCcsAcctQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 信用卡-历史表-第一币种贷记帐户数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 03:51:17
 */
@Repository
public class LbHCcsAcctDaoImpl extends AbstractBaseDaoImpl<LbHCcsAcctDo, LbHCcsAcctMapper> implements LbHCcsAcctDao {
    /**
     * 分页查询
     *
     * @param lbHCcsAcct 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCcsAcctDo> selectPage(LbHCcsAcctQuery lbHCcsAcct, PageParam pageParam) {
        LbHCcsAcctExample example = buildExample(lbHCcsAcct);
        return PageHelper.<LbHCcsAcctDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询信用卡-历史表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @param dataDate
     * @return
     */
    @Override
    public LbHCcsAcctDo selectByKey(Integer xaccount, Integer bank, String dataDate) {
        LbHCcsAcctKeyDo lbHCcsAcctKeyDo = new LbHCcsAcctKeyDo();
        lbHCcsAcctKeyDo.setXaccount(xaccount);
        lbHCcsAcctKeyDo.setBank(bank);
        lbHCcsAcctKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHCcsAcctKeyDo);
    }

    /**
     * 根据key删除信用卡-历史表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(Integer xaccount, Integer bank, String dataDate) {
        LbHCcsAcctKeyDo lbHCcsAcctKeyDo = new LbHCcsAcctKeyDo();
        lbHCcsAcctKeyDo.setXaccount(xaccount);
        lbHCcsAcctKeyDo.setBank(bank);
        lbHCcsAcctKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHCcsAcctKeyDo);
    }

    /**
     * 查询信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct 条件
     * @return List<LbHCcsAcctDo>
     */
    @Override
    public List<LbHCcsAcctDo> selectByExample(LbHCcsAcctQuery lbHCcsAcct) {
        return getMapper().selectByExample(buildExample(lbHCcsAcct));
    }

    /**
     * 新增信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCcsAcctDo lbHCcsAcct) {
        if (lbHCcsAcct == null) {
            return -1;
        }
        return getMapper().insertSelective(lbHCcsAcct);
    }

    /**
     * 修改信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct
     * @return
     */
    @Override
    public int updateBySelective(LbHCcsAcctDo lbHCcsAcct) {
        if (lbHCcsAcct == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHCcsAcct);
    }

    @Override
    public int updateBySelectiveByExample(LbHCcsAcctDo lbHCcsAcct, LbHCcsAcctQuery lbHCcsAcctQuery) {
        return getMapper().updateByExampleSelective(lbHCcsAcct, buildExample(lbHCcsAcctQuery));
    }

    /**
     * 构建信用卡-历史表-第一币种贷记帐户Example信息
     *
     * @param lbHCcsAcct
     * @return
     */
    public LbHCcsAcctExample buildExample(LbHCcsAcctQuery lbHCcsAcct) {
        LbHCcsAcctExample example = new LbHCcsAcctExample();
        LbHCcsAcctExample.Criteria criteria = example.createCriteria();
        if (lbHCcsAcct != null) {
            //添加查询条件
            if (null != lbHCcsAcct.getXaccount()) {
                criteria.andXaccountEqualTo(lbHCcsAcct.getXaccount());
            }
            if (null != lbHCcsAcct.getBank()) {
                criteria.andBankEqualTo(lbHCcsAcct.getBank());
            }
            if (StringUtil.isNotEmpty(lbHCcsAcct.getCustrNbr())) {
                criteria.andCustrNbrEqualTo(lbHCcsAcct.getCustrNbr());
            }
            if (null != lbHCcsAcct.getBalCmpint()) {
                criteria.andBalCmpintEqualTo(lbHCcsAcct.getBalCmpint());
            }
            if (null != lbHCcsAcct.getBalFree()) {
                criteria.andBalFreeEqualTo(lbHCcsAcct.getBalFree());
            }
            if (null != lbHCcsAcct.getBalInt()) {
                criteria.andBalIntEqualTo(lbHCcsAcct.getBalInt());
            }
            if (null != lbHCcsAcct.getBalNoint()) {
                criteria.andBalNointEqualTo(lbHCcsAcct.getBalNoint());
            }
            if (null != lbHCcsAcct.getBalOrint()) {
                criteria.andBalOrintEqualTo(lbHCcsAcct.getBalOrint());
            }
            if (null != lbHCcsAcct.getCredLimit()) {
                criteria.andCredLimitEqualTo(lbHCcsAcct.getCredLimit());
            }
            if (null != lbHCcsAcct.getMpRemPpl()) {
                criteria.andMpRemPplEqualTo(lbHCcsAcct.getMpRemPpl());
            }
            if (null != lbHCcsAcct.getStmBalfre()) {
                criteria.andStmBalfreEqualTo(lbHCcsAcct.getStmBalfre());
            }
            if (null != lbHCcsAcct.getStmBalint()) {
                criteria.andStmBalintEqualTo(lbHCcsAcct.getStmBalint());
            }
            if (null != lbHCcsAcct.getStmBalori()) {
                criteria.andStmBaloriEqualTo(lbHCcsAcct.getStmBalori());
            }
            if (null != lbHCcsAcct.getStmNoint()) {
                criteria.andStmNointEqualTo(lbHCcsAcct.getStmNoint());
            }
            if (null != lbHCcsAcct.getBalMp()) {
                criteria.andBalMpEqualTo(lbHCcsAcct.getBalMp());
            }
            if (null != lbHCcsAcct.getStmBalmp()) {
                criteria.andStmBalmpEqualTo(lbHCcsAcct.getStmBalmp());
            }
            if (StringUtil.isNotEmpty(lbHCcsAcct.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCcsAcct.getDataDate());
            }
        }
        buildExampleExt(lbHCcsAcct, criteria);
        return example;
    }

    /**
     * 构建信用卡-历史表-第一币种贷记帐户ExampleExt方法
     *
     * @param lbHCcsAcct
     * @return
     */
    public void buildExampleExt(LbHCcsAcctQuery lbHCcsAcct, LbHCcsAcctExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 清空信用卡历史表所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
