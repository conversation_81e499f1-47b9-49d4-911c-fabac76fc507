<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbSCorePprodMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbSCorePprodDo">
        <result property="faredm" column="faredm" jdbcType="VARCHAR"/> <!-- 法人代码 -->
        <result property="chapbh" column="chapbh" jdbcType="VARCHAR"/> <!-- 产品编号 -->
        <result property="chapmx" column="chapmx" jdbcType="VARCHAR"/> <!-- 产品描述 -->
        <result property="module" column="module" jdbcType="VARCHAR"/> <!-- 模块 -->
        <result property="weihrq" column="weihrq" jdbcType="VARCHAR"/> <!-- 维护日期 -->
        <result property="weihsj" column="weihsj" jdbcType="DECIMAL"/> <!-- 维护时间 -->
        <result property="weihgy" column="weihgy" jdbcType="VARCHAR"/> <!-- 维护柜员 -->
        <result property="weihjg" column="weihjg" jdbcType="VARCHAR"/> <!-- 维护机构 -->
        <result property="rowidd" column="rowidd" jdbcType="VARCHAR"/> <!-- 序列号 -->
        <result property="shjnch" column="shjnch" jdbcType="DECIMAL"/> <!-- 时间戳 -->
        <result property="jiluzt" column="jiluzt" jdbcType="VARCHAR"/> <!-- 记录状态 -->
    </resultMap>
    <sql id="Base_Column_List">
        faredm
        , chapbh
        , chapmx
        , module
        , weihrq
        , weihsj
        , weihgy
        , weihjg
        , rowidd
        , shjnch
        , jiluzt
    </sql>
    <!-- 批量插入产品定义信息 - 性能优化：使用标准批量INSERT语法，修复表名 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO lb_s_core_pprod (
            faredm, chapbh, chapmx, module, weihrq, weihsj, weihgy, weihjg, rowidd, shjnch, jiluzt
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.faredm, jdbcType=VARCHAR},
             #{item.chapbh, jdbcType=VARCHAR},
             #{item.chapmx, jdbcType=VARCHAR},
             #{item.module, jdbcType=VARCHAR},
             #{item.weihrq, jdbcType=VARCHAR},
             #{item.weihsj, jdbcType=DECIMAL},
             #{item.weihgy, jdbcType=VARCHAR},
             #{item.weihjg, jdbcType=VARCHAR},
             #{item.rowidd, jdbcType=VARCHAR},
             #{item.shjnch, jdbcType=DECIMAL},
             #{item.jiluzt, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!-- 清空产品定义表所有数据 -->
    <delete id="deleteAll" parameterType="java.lang.String">
        TRUNCATE TABLE lb_s_core_pprod
    </delete>
</mapper>
