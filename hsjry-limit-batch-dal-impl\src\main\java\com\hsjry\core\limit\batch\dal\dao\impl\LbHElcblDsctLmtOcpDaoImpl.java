package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblDsctLmtOcpDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblDsctLmtOcpMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctLmtOcpKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctLmtOcpExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblDsctLmtOcpQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 电票系统-历史表-日终贴现额度占用同步数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHElcblDsctLmtOcpDaoImpl extends AbstractBaseDaoImpl<LbHElcblDsctLmtOcpDo, LbHElcblDsctLmtOcpMapper>
    implements LbHElcblDsctLmtOcpDao {
    /**
     * 分页查询
     *
     * @param lbHElcblDsctLmtOcp 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHElcblDsctLmtOcpDo> selectPage(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcp, PageParam pageParam) {
        LbHElcblDsctLmtOcpExample example = buildExample(lbHElcblDsctLmtOcp);
        return PageHelper.<LbHElcblDsctLmtOcpDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public LbHElcblDsctLmtOcpDo selectByKey(String billNumb, String billRangeStart, String billRangeEnd,
        String dataDate) {
        LbHElcblDsctLmtOcpKeyDo lbHElcblDsctLmtOcpKeyDo = new LbHElcblDsctLmtOcpKeyDo();
        lbHElcblDsctLmtOcpKeyDo.setBillNumb(billNumb);
        lbHElcblDsctLmtOcpKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblDsctLmtOcpKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblDsctLmtOcpKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHElcblDsctLmtOcpKeyDo);
    }

    /**
     * 根据key删除电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String billNumb, String billRangeStart, String billRangeEnd, String dataDate) {
        LbHElcblDsctLmtOcpKeyDo lbHElcblDsctLmtOcpKeyDo = new LbHElcblDsctLmtOcpKeyDo();
        lbHElcblDsctLmtOcpKeyDo.setBillNumb(billNumb);
        lbHElcblDsctLmtOcpKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblDsctLmtOcpKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblDsctLmtOcpKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHElcblDsctLmtOcpKeyDo);
    }

    /**
     * 查询电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp 条件
     * @return List<LbHElcblDsctLmtOcpDo>
     */
    @Override
    public List<LbHElcblDsctLmtOcpDo> selectByExample(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcp) {
        return getMapper().selectByExample(buildExample(lbHElcblDsctLmtOcp));
    }

    /**
     * 新增电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp) {
        if (lbHElcblDsctLmtOcp == null) {
            return -1;
        }
        return getMapper().insertSelective(lbHElcblDsctLmtOcp);
    }

    /**
     * 修改电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp
     * @return
     */
    @Override
    public int updateBySelective(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp) {
        if (lbHElcblDsctLmtOcp == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHElcblDsctLmtOcp);
    }

    @Override
    public int updateBySelectiveByExample(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp,
        LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcpQuery) {
        return getMapper().updateByExampleSelective(lbHElcblDsctLmtOcp, buildExample(lbHElcblDsctLmtOcpQuery));
    }

    /**
     * 构建电票系统-历史表-日终贴现额度占用同步Example信息
     *
     * @param lbHElcblDsctLmtOcp
     * @return
     */
    public LbHElcblDsctLmtOcpExample buildExample(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcp) {
        LbHElcblDsctLmtOcpExample example = new LbHElcblDsctLmtOcpExample();
        LbHElcblDsctLmtOcpExample.Criteria criteria = example.createCriteria();
        if (lbHElcblDsctLmtOcp != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getSmBsnUserCertificateNo())) {
                criteria.andSmBsnUserCertificateNoEqualTo(lbHElcblDsctLmtOcp.getSmBsnUserCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getDataDate())) {
                criteria.andDataDateEqualTo(lbHElcblDsctLmtOcp.getDataDate());
            }
            if (null != lbHElcblDsctLmtOcp.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbHElcblDsctLmtOcp.getUseOccupyAmount());
            }
            if (null != lbHElcblDsctLmtOcp.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbHElcblDsctLmtOcp.getAvailableAmount());
            }
            if (null != lbHElcblDsctLmtOcp.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbHElcblDsctLmtOcp.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getSmBsnLimitType())) {
                criteria.andSmBsnLimitTypeEqualTo(lbHElcblDsctLmtOcp.getSmBsnLimitType());
            }
            if (null != lbHElcblDsctLmtOcp.getBillAmount()) {
                criteria.andBillAmountEqualTo(lbHElcblDsctLmtOcp.getBillAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getCurrency())) {
                criteria.andCurrencyEqualTo(lbHElcblDsctLmtOcp.getCurrency());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getCustMgrNm())) {
                criteria.andCustMgrNmEqualTo(lbHElcblDsctLmtOcp.getCustMgrNm());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getCustMgrNumb())) {
                criteria.andCustMgrNumbEqualTo(lbHElcblDsctLmtOcp.getCustMgrNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getBatchNumb())) {
                criteria.andBatchNumbEqualTo(lbHElcblDsctLmtOcp.getBatchNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getSmBsnUserCertificateKind())) {
                criteria.andSmBsnUserCertificateKindEqualTo(lbHElcblDsctLmtOcp.getSmBsnUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getSmBsnUserId())) {
                criteria.andSmBsnUserIdEqualTo(lbHElcblDsctLmtOcp.getSmBsnUserId());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbHElcblDsctLmtOcp.getCoreInstNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getBranchNo())) {
                criteria.andBranchNoEqualTo(lbHElcblDsctLmtOcp.getBranchNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getRelationId())) {
                criteria.andRelationIdEqualTo(lbHElcblDsctLmtOcp.getRelationId());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getBillRangeEnd())) {
                criteria.andBillRangeEndEqualTo(lbHElcblDsctLmtOcp.getBillRangeEnd());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getBillRangeStart())) {
                criteria.andBillRangeStartEqualTo(lbHElcblDsctLmtOcp.getBillRangeStart());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getBillNumb())) {
                criteria.andBillNumbEqualTo(lbHElcblDsctLmtOcp.getBillNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctLmtOcp.getProductId())) {
                criteria.andProductIdEqualTo(lbHElcblDsctLmtOcp.getProductId());
            }
        }
        buildExampleExt(lbHElcblDsctLmtOcp, criteria);
        return example;
    }

    /**
     * 构建电票系统-历史表-日终贴现额度占用同步ExampleExt方法
     *
     * @param lbHElcblDsctLmtOcp
     * @return
     */
    public void buildExampleExt(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcp,
        LbHElcblDsctLmtOcpExample.Criteria criteria) {

        //自定义实现
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
