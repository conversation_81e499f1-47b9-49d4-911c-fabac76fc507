package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreBcdhpDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreBcdhpMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreBcdhpQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 核心系统-落地表-银行承兑汇票产品定义数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHCoreBcdhpDaoImpl extends AbstractBaseDaoImpl<LbHCoreBcdhpDo, LbHCoreBcdhpMapper>
    implements LbHCoreBcdhpDao {
    /**
     * 分页查询
     *
     * @param lbHCoreBcdhp 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCoreBcdhpDo> selectPage(LbHCoreBcdhpQuery lbHCoreBcdhp, PageParam pageParam) {
        LbHCoreBcdhpExample example = buildExample(lbHCoreBcdhp);
        return PageHelper.<LbHCoreBcdhpDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统-落地表-银行承兑汇票产品定义
     *
     * @param faredm
     * @param cdxybh
     * @param bccppc
     * @param chupbh
     * @param dataDate
     * @return
     */
    @Override
    public LbHCoreBcdhpDo selectByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh,
        String dataDate) {
        LbHCoreBcdhpKeyDo lbHCoreBcdhpKeyDo = new LbHCoreBcdhpKeyDo();
        lbHCoreBcdhpKeyDo.setFaredm(faredm);
        lbHCoreBcdhpKeyDo.setCdxybh(cdxybh);
        lbHCoreBcdhpKeyDo.setBccppc(bccppc);
        lbHCoreBcdhpKeyDo.setChupbh(chupbh);
        lbHCoreBcdhpKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHCoreBcdhpKeyDo);
    }

    /**
     * 根据key删除核心系统-落地表-银行承兑汇票产品定义
     *
     * @param faredm
     * @param cdxybh
     * @param bccppc
     * @param chupbh
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh, String dataDate) {
        LbHCoreBcdhpKeyDo lbHCoreBcdhpKeyDo = new LbHCoreBcdhpKeyDo();
        lbHCoreBcdhpKeyDo.setFaredm(faredm);
        lbHCoreBcdhpKeyDo.setCdxybh(cdxybh);
        lbHCoreBcdhpKeyDo.setBccppc(bccppc);
        lbHCoreBcdhpKeyDo.setChupbh(chupbh);
        lbHCoreBcdhpKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHCoreBcdhpKeyDo);
    }

    /**
     * 查询核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp 条件
     * @return List<LbHCoreBcdhpDo>
     */
    @Override
    public List<LbHCoreBcdhpDo> selectByExample(LbHCoreBcdhpQuery lbHCoreBcdhp) {
        return getMapper().selectByExample(buildExample(lbHCoreBcdhp));
    }

    /**
     * 新增核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCoreBcdhpDo lbHCoreBcdhp) {
        if (lbHCoreBcdhp == null) {
            return -1;
        }

        return getMapper().insertSelective(lbHCoreBcdhp);
    }

    /**
     * 修改核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp
     * @return
     */
    @Override
    public int updateBySelective(LbHCoreBcdhpDo lbHCoreBcdhp) {
        if (lbHCoreBcdhp == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHCoreBcdhp);
    }

    @Override
    public int updateBySelectiveByExample(LbHCoreBcdhpDo lbHCoreBcdhp, LbHCoreBcdhpQuery lbHCoreBcdhpQuery) {
        return getMapper().updateByExampleSelective(lbHCoreBcdhp, buildExample(lbHCoreBcdhpQuery));
    }

    /**
     * 构建核心系统-落地表-银行承兑汇票产品定义Example信息
     *
     * @param lbHCoreBcdhp
     * @return
     */
    public LbHCoreBcdhpExample buildExample(LbHCoreBcdhpQuery lbHCoreBcdhp) {
        LbHCoreBcdhpExample example = new LbHCoreBcdhpExample();
        LbHCoreBcdhpExample.Criteria criteria = example.createCriteria();
        if (lbHCoreBcdhp != null) {
            //添加查询条件
            if (null != lbHCoreBcdhp.getMxxhao()) {
                criteria.andMxxhaoEqualTo(lbHCoreBcdhp.getMxxhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getDnknbz())) {
                criteria.andDnknbzEqualTo(lbHCoreBcdhp.getDnknbz());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getJiejuh())) {
                criteria.andJiejuhEqualTo(lbHCoreBcdhp.getJiejuh());
            }
            if (null != lbHCoreBcdhp.getZhdkje()) {
                criteria.andZhdkjeEqualTo(lbHCoreBcdhp.getZhdkje());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getZijnqx())) {
                criteria.andZijnqxEqualTo(lbHCoreBcdhp.getZijnqx());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getDczrzh())) {
                criteria.andDczrzhEqualTo(lbHCoreBcdhp.getDczrzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getSkzhao())) {
                criteria.andSkzhaoEqualTo(lbHCoreBcdhp.getSkzhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getChipmc())) {
                criteria.andChipmcEqualTo(lbHCoreBcdhp.getChipmc());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getKaihhh())) {
                criteria.andKaihhhEqualTo(lbHCoreBcdhp.getKaihhh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getKaihhm())) {
                criteria.andKaihhmEqualTo(lbHCoreBcdhp.getKaihhm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getSxiorq())) {
                criteria.andSxiorqEqualTo(lbHCoreBcdhp.getSxiorq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWjflrq())) {
                criteria.andWjflrqEqualTo(lbHCoreBcdhp.getWjflrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWjdkfl())) {
                criteria.andWjdkflEqualTo(lbHCoreBcdhp.getWjdkfl());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getYchpzt())) {
                criteria.andYchpztEqualTo(lbHCoreBcdhp.getYchpzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getPiojzt())) {
                criteria.andPiojztEqualTo(lbHCoreBcdhp.getPiojzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getSfdzpj())) {
                criteria.andSfdzpjEqualTo(lbHCoreBcdhp.getSfdzpj());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getRuzhrq())) {
                criteria.andRuzhrqEqualTo(lbHCoreBcdhp.getRuzhrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getGsriqi())) {
                criteria.andGsriqiEqualTo(lbHCoreBcdhp.getGsriqi());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getJgriqi())) {
                criteria.andJgriqiEqualTo(lbHCoreBcdhp.getJgriqi());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getRemark())) {
                criteria.andRemarkEqualTo(lbHCoreBcdhp.getRemark());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getBeizxx())) {
                criteria.andBeizxxEqualTo(lbHCoreBcdhp.getBeizxx());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getKaihjg())) {
                criteria.andKaihjgEqualTo(lbHCoreBcdhp.getKaihjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getKaihgy())) {
                criteria.andKaihgyEqualTo(lbHCoreBcdhp.getKaihgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbHCoreBcdhp.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getXiohgy())) {
                criteria.andXiohgyEqualTo(lbHCoreBcdhp.getXiohgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbHCoreBcdhp.getWeihjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbHCoreBcdhp.getWeihrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getXiohrq())) {
                criteria.andXiohrqEqualTo(lbHCoreBcdhp.getXiohrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getWeihsj())) {
                criteria.andWeihsjEqualTo(lbHCoreBcdhp.getWeihsj());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getShjnch())) {
                criteria.andShjnchEqualTo(lbHCoreBcdhp.getShjnch());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getJiluzt())) {
                criteria.andJiluztEqualTo(lbHCoreBcdhp.getJiluzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCoreBcdhp.getDataDate());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getQfhlhm())) {
                criteria.andQfhlhmEqualTo(lbHCoreBcdhp.getQfhlhm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getCdxybh())) {
                criteria.andCdxybhEqualTo(lbHCoreBcdhp.getCdxybh());
            }
            if (null != lbHCoreBcdhp.getBccppc()) {
                criteria.andBccppcEqualTo(lbHCoreBcdhp.getBccppc());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getChupbh())) {
                criteria.andChupbhEqualTo(lbHCoreBcdhp.getChupbh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getYngyjg())) {
                criteria.andYngyjgEqualTo(lbHCoreBcdhp.getYngyjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getZhngjg())) {
                criteria.andZhngjgEqualTo(lbHCoreBcdhp.getZhngjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getHtngbh())) {
                criteria.andHtngbhEqualTo(lbHCoreBcdhp.getHtngbh());
            }
            if (null != lbHCoreBcdhp.getZongje()) {
                criteria.andZongjeEqualTo(lbHCoreBcdhp.getZongje());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getPngzzl())) {
                criteria.andPngzzlEqualTo(lbHCoreBcdhp.getPngzzl());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getPjlugz())) {
                criteria.andPjlugzEqualTo(lbHCoreBcdhp.getPjlugz());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getPiojhm())) {
                criteria.andPiojhmEqualTo(lbHCoreBcdhp.getPiojhm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getHuobdh())) {
                criteria.andHuobdhEqualTo(lbHCoreBcdhp.getHuobdh());
            }
            if (null != lbHCoreBcdhp.getPiomje()) {
                criteria.andPiomjeEqualTo(lbHCoreBcdhp.getPiomje());
            }
            if (null != lbHCoreBcdhp.getHuipje()) {
                criteria.andHuipjeEqualTo(lbHCoreBcdhp.getHuipje());
            }
            if (null != lbHCoreBcdhp.getBeikje()) {
                criteria.andBeikjeEqualTo(lbHCoreBcdhp.getBeikje());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getQfhlhh())) {
                criteria.andQfhlhhEqualTo(lbHCoreBcdhp.getQfhlhh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getFaredm())) {
                criteria.andFaredmEqualTo(lbHCoreBcdhp.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getKehhao())) {
                criteria.andKehhaoEqualTo(lbHCoreBcdhp.getKehhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getChprzh())) {
                criteria.andChprzhEqualTo(lbHCoreBcdhp.getChprzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getChprqc())) {
                criteria.andChprqcEqualTo(lbHCoreBcdhp.getChprqc());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getSkrkhh())) {
                criteria.andSkrkhhEqualTo(lbHCoreBcdhp.getSkrkhh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getShkrzh())) {
                criteria.andShkrzhEqualTo(lbHCoreBcdhp.getShkrzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getShkrxm())) {
                criteria.andShkrxmEqualTo(lbHCoreBcdhp.getShkrxm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getSkhhao())) {
                criteria.andSkhhaoEqualTo(lbHCoreBcdhp.getSkhhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getShkhhm())) {
                criteria.andShkhhmEqualTo(lbHCoreBcdhp.getShkhhm());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getShfobz())) {
                criteria.andShfobzEqualTo(lbHCoreBcdhp.getShfobz());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getEdceng())) {
                criteria.andEdcengEqualTo(lbHCoreBcdhp.getEdceng());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getYcqffs())) {
                criteria.andYcqffsEqualTo(lbHCoreBcdhp.getYcqffs());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getJioyrq())) {
                criteria.andJioyrqEqualTo(lbHCoreBcdhp.getJioyrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getDaoqrq())) {
                criteria.andDaoqrqEqualTo(lbHCoreBcdhp.getDaoqrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getQnfarq())) {
                criteria.andQnfarqEqualTo(lbHCoreBcdhp.getQnfarq());
            }
            if (StringUtil.isNotEmpty(lbHCoreBcdhp.getCduirq())) {
                criteria.andCduirqEqualTo(lbHCoreBcdhp.getCduirq());
            }
        }
        buildExampleExt(lbHCoreBcdhp, criteria);
        return example;
    }

    /**
     * 构建核心系统-落地表-银行承兑汇票产品定义ExampleExt方法
     *
     * @param lbHCoreBcdhp
     * @return
     */
    public void buildExampleExt(LbHCoreBcdhpQuery lbHCoreBcdhp, LbHCoreBcdhpExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 清空核心系统-落地表-银行承兑汇票产品定义所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

    @Override
    public int insertList(List<LbHCoreBcdhpDo> lbHCoreBcdhpList) {
        if (lbHCoreBcdhpList == null || lbHCoreBcdhpList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbHCoreBcdhpList);
    }

}
