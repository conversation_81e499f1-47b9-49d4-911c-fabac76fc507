package com.hsjry.core.limit.batch.dal.dao.mapper;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

import java.util.List;

/**
 * 核心系统产品定义表-落地表（存储产品基础定义信息）mapper
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCorePprodMapper extends CommonMapper<LbHCorePprodDo> {
    int deleteAll();
    
    /**
     * 根据数据日期删除核心产品定义表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(@Param("dataDate") String dataDate);

    /**
     * 批量插入核心系统产品定义信息
     *
     * @param list 核心系统产品定义信息列表
     * @return int
     */
    int insertList(@Param("list") List<LbHCorePprodDo> list);
}
