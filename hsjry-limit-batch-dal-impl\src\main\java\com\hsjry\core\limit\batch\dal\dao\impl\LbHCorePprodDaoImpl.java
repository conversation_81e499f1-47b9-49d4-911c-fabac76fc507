package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCorePprodDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCorePprodMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCorePprodQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 核心系统产品定义表-落地表（存储产品基础定义信息）数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHCorePprodDaoImpl extends AbstractBaseDaoImpl<LbHCorePprodDo, LbHCorePprodMapper>
    implements LbHCorePprodDao {
    /**
     * 分页查询
     *
     * @param lbHCorePprod 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCorePprodDo> selectPage(LbHCorePprodQuery lbHCorePprod, PageParam pageParam) {
        LbHCorePprodExample example = buildExample(lbHCorePprod);
        return PageHelper.<LbHCorePprodDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统产品定义表-落地表（存储产品基础定义信息）
     *
     * @param chapbh
     * @param dataDate
     * @return
     */
    @Override
    public LbHCorePprodDo selectByKey(String chapbh, String dataDate) {
        LbHCorePprodKeyDo lbHCorePprodKeyDo = new LbHCorePprodKeyDo();
        lbHCorePprodKeyDo.setChapbh(chapbh);
        lbHCorePprodKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHCorePprodKeyDo);
    }

    /**
     * 根据key删除核心系统产品定义表-落地表（存储产品基础定义信息）
     *
     * @param chapbh
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String chapbh, String dataDate) {
        LbHCorePprodKeyDo lbHCorePprodKeyDo = new LbHCorePprodKeyDo();
        lbHCorePprodKeyDo.setChapbh(chapbh);
        lbHCorePprodKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHCorePprodKeyDo);
    }

    /**
     * 查询核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod 条件
     * @return List<LbHCorePprodDo>
     */
    @Override
    public List<LbHCorePprodDo> selectByExample(LbHCorePprodQuery lbHCorePprod) {
        return getMapper().selectByExample(buildExample(lbHCorePprod));
    }

    /**
     * 新增核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCorePprodDo lbHCorePprod) {
        if (lbHCorePprod == null) {
            return -1;
        }
        return getMapper().insertSelective(lbHCorePprod);
    }

    /**
     * 修改核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod
     * @return
     */
    @Override
    public int updateBySelective(LbHCorePprodDo lbHCorePprod) {
        if (lbHCorePprod == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHCorePprod);
    }

    @Override
    public int updateBySelectiveByExample(LbHCorePprodDo lbHCorePprod, LbHCorePprodQuery lbHCorePprodQuery) {
        return getMapper().updateByExampleSelective(lbHCorePprod, buildExample(lbHCorePprodQuery));
    }

    @Override
    public int deleteAll()  {
        return getMapper().deleteAll();
    }

    /**
     * 构建核心系统产品定义表-落地表（存储产品基础定义信息）Example信息
     *
     * @param lbHCorePprod
     * @return
     */
    public LbHCorePprodExample buildExample(LbHCorePprodQuery lbHCorePprod) {
        LbHCorePprodExample example = new LbHCorePprodExample();
        LbHCorePprodExample.Criteria criteria = example.createCriteria();
        if (lbHCorePprod != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHCorePprod.getFaredm())) {
                criteria.andFaredmEqualTo(lbHCorePprod.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getChapbh())) {
                criteria.andChapbhEqualTo(lbHCorePprod.getChapbh());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getChapmx())) {
                criteria.andChapmxEqualTo(lbHCorePprod.getChapmx());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getModule())) {
                criteria.andModuleEqualTo(lbHCorePprod.getModule());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbHCorePprod.getWeihrq());
            }
            if (null != lbHCorePprod.getWeihsj()) {
                criteria.andWeihsjEqualTo(lbHCorePprod.getWeihsj());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbHCorePprod.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbHCorePprod.getWeihjg());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getRowidd())) {
                criteria.andRowiddEqualTo(lbHCorePprod.getRowidd());
            }
            if (null != lbHCorePprod.getShjnch()) {
                criteria.andShjnchEqualTo(lbHCorePprod.getShjnch());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getJiluzt())) {
                criteria.andJiluztEqualTo(lbHCorePprod.getJiluzt());
            }
            if (StringUtil.isNotEmpty(lbHCorePprod.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCorePprod.getDataDate());
            }
        }
        buildExampleExt(lbHCorePprod, criteria);
        return example;
    }

    /**
     * 构建核心系统产品定义表-落地表（存储产品基础定义信息）ExampleExt方法
     *
     * @param lbHCorePprod
     * @return
     */
    public void buildExampleExt(LbHCorePprodQuery lbHCorePprod, LbHCorePprodExample.Criteria criteria) {

        //自定义实现
    }

    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

    @Override
    public int insertList(List<LbHCorePprodDo> lbHCorePprodList) {
        if (lbHCorePprodList == null || lbHCorePprodList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbHCorePprodList);
    }
}
