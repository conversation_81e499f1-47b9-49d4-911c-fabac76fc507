package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblTfrdcnLmtRlseDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblTfrdcnLmtRlseMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtRlseDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtRlseKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtRlseExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblTfrdcnLmtRlseQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 电票日终转贴现额度释放同步-历史数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHElcblTfrdcnLmtRlseDaoImpl
    extends AbstractBaseDaoImpl<LbHElcblTfrdcnLmtRlseDo, LbHElcblTfrdcnLmtRlseMapper>
    implements LbHElcblTfrdcnLmtRlseDao {
    /**
     * 分页查询
     *
     * @param lbHElcblTfrdcnLmtRlse 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHElcblTfrdcnLmtRlseDo> selectPage(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlse,
        PageParam pageParam) {
        LbHElcblTfrdcnLmtRlseExample example = buildExample(lbHElcblTfrdcnLmtRlse);
        return PageHelper.<LbHElcblTfrdcnLmtRlseDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票日终转贴现额度释放同步-历史
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public LbHElcblTfrdcnLmtRlseDo selectByKey(String batchNumb, String billNumb, String billRangeStart,
        String billRangeEnd, String dataDate) {
        LbHElcblTfrdcnLmtRlseKeyDo lbHElcblTfrdcnLmtRlseKeyDo = new LbHElcblTfrdcnLmtRlseKeyDo();
        lbHElcblTfrdcnLmtRlseKeyDo.setBatchNumb(batchNumb);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillNumb(billNumb);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblTfrdcnLmtRlseKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHElcblTfrdcnLmtRlseKeyDo);
    }

    /**
     * 根据key删除电票日终转贴现额度释放同步-历史
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd,
        String dataDate) {
        LbHElcblTfrdcnLmtRlseKeyDo lbHElcblTfrdcnLmtRlseKeyDo = new LbHElcblTfrdcnLmtRlseKeyDo();
        lbHElcblTfrdcnLmtRlseKeyDo.setBatchNumb(batchNumb);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillNumb(billNumb);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillRangeStart(billRangeStart);
        lbHElcblTfrdcnLmtRlseKeyDo.setBillRangeEnd(billRangeEnd);
        lbHElcblTfrdcnLmtRlseKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHElcblTfrdcnLmtRlseKeyDo);
    }

    /**
     * 查询电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse 条件
     * @return List<LbHElcblTfrdcnLmtRlseDo>
     */
    @Override
    public List<LbHElcblTfrdcnLmtRlseDo> selectByExample(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlse) {
        return getMapper().selectByExample(buildExample(lbHElcblTfrdcnLmtRlse));
    }

    /**
     * 新增电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse) {
        if (lbHElcblTfrdcnLmtRlse == null) {
            return -1;
        }

        return getMapper().insertSelective(lbHElcblTfrdcnLmtRlse);
    }

    /**
     * 修改电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse
     * @return
     */
    @Override
    public int updateBySelective(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse) {
        if (lbHElcblTfrdcnLmtRlse == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbHElcblTfrdcnLmtRlse);
    }

    @Override
    public int updateBySelectiveByExample(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse,
        LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlseQuery) {
        return getMapper().updateByExampleSelective(lbHElcblTfrdcnLmtRlse, buildExample(lbHElcblTfrdcnLmtRlseQuery));
    }

    /**
     * 构建电票日终转贴现额度释放同步-历史Example信息
     *
     * @param lbHElcblTfrdcnLmtRlse
     * @return
     */
    public LbHElcblTfrdcnLmtRlseExample buildExample(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlse) {
        LbHElcblTfrdcnLmtRlseExample example = new LbHElcblTfrdcnLmtRlseExample();
        LbHElcblTfrdcnLmtRlseExample.Criteria criteria = example.createCriteria();
        if (lbHElcblTfrdcnLmtRlse != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getSmBsnUserCertificateNo())) {
                criteria.andSmBsnUserCertificateNoEqualTo(lbHElcblTfrdcnLmtRlse.getSmBsnUserCertificateNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getDataDate())) {
                criteria.andDataDateEqualTo(lbHElcblTfrdcnLmtRlse.getDataDate());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getCustMgrNm())) {
                criteria.andCustMgrNmEqualTo(lbHElcblTfrdcnLmtRlse.getCustMgrNm());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getCustMgrNumb())) {
                criteria.andCustMgrNumbEqualTo(lbHElcblTfrdcnLmtRlse.getCustMgrNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbHElcblTfrdcnLmtRlse.getCoreInstNo());
            }
            if (null != lbHElcblTfrdcnLmtRlse.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbHElcblTfrdcnLmtRlse.getUseOccupyAmount());
            }
            if (null != lbHElcblTfrdcnLmtRlse.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbHElcblTfrdcnLmtRlse.getAvailableAmount());
            }
            if (null != lbHElcblTfrdcnLmtRlse.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbHElcblTfrdcnLmtRlse.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getSmBsnLimitType())) {
                criteria.andSmBsnLimitTypeEqualTo(lbHElcblTfrdcnLmtRlse.getSmBsnLimitType());
            }
            if (null != lbHElcblTfrdcnLmtRlse.getBillAmount()) {
                criteria.andBillAmountEqualTo(lbHElcblTfrdcnLmtRlse.getBillAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getCurrency())) {
                criteria.andCurrencyEqualTo(lbHElcblTfrdcnLmtRlse.getCurrency());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getBatchNumb())) {
                criteria.andBatchNumbEqualTo(lbHElcblTfrdcnLmtRlse.getBatchNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getSmBsnUserCertificateKind())) {
                criteria.andSmBsnUserCertificateKindEqualTo(lbHElcblTfrdcnLmtRlse.getSmBsnUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getSmBsnUserId())) {
                criteria.andSmBsnUserIdEqualTo(lbHElcblTfrdcnLmtRlse.getSmBsnUserId());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getBranchNo())) {
                criteria.andBranchNoEqualTo(lbHElcblTfrdcnLmtRlse.getBranchNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getRelationId())) {
                criteria.andRelationIdEqualTo(lbHElcblTfrdcnLmtRlse.getRelationId());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getBillRangeEnd())) {
                criteria.andBillRangeEndEqualTo(lbHElcblTfrdcnLmtRlse.getBillRangeEnd());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getBillRangeStart())) {
                criteria.andBillRangeStartEqualTo(lbHElcblTfrdcnLmtRlse.getBillRangeStart());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getBillNumb())) {
                criteria.andBillNumbEqualTo(lbHElcblTfrdcnLmtRlse.getBillNumb());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getLimitReleaseType())) {
                criteria.andLimitReleaseTypeEqualTo(lbHElcblTfrdcnLmtRlse.getLimitReleaseType());
            }
            if (StringUtil.isNotEmpty(lbHElcblTfrdcnLmtRlse.getProductId())) {
                criteria.andProductIdEqualTo(lbHElcblTfrdcnLmtRlse.getProductId());
            }
        }
        buildExampleExt(lbHElcblTfrdcnLmtRlse, criteria);
        return example;
    }

    /**
     * 构建电票日终转贴现额度释放同步-历史ExampleExt方法
     *
     * @param lbHElcblTfrdcnLmtRlse
     * @return
     */
    public void buildExampleExt(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlse,
        LbHElcblTfrdcnLmtRlseExample.Criteria criteria) {

        //自定义实现
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
