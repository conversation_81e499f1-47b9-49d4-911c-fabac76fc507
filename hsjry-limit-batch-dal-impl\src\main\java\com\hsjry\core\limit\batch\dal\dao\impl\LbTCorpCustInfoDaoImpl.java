package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCorpCustInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTCorpCustInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCorpCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCorpCustInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCorpCustInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCorpCustInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-对公客户信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbTCorpCustInfoDaoImpl extends AbstractBaseDaoImpl<LbTCorpCustInfoDo, LbTCorpCustInfoMapper>
    implements LbTCorpCustInfoDao {
    /**
     * 分页查询
     *
     * @param lbTCorpCustInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTCorpCustInfoDo> selectPage(LbTCorpCustInfoQuery lbTCorpCustInfo, PageParam pageParam) {
        LbTCorpCustInfoExample example = buildExample(lbTCorpCustInfo);
        return PageHelper.<LbTCorpCustInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-对公客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public LbTCorpCustInfoDo selectByKey(String custNo) {
        LbTCorpCustInfoKeyDo lbTCorpCustInfoKeyDo = new LbTCorpCustInfoKeyDo();
        lbTCorpCustInfoKeyDo.setCustNo(custNo);
        lbTCorpCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTCorpCustInfoKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-对公客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public int deleteByKey(String custNo) {
        LbTCorpCustInfoKeyDo lbTCorpCustInfoKeyDo = new LbTCorpCustInfoKeyDo();
        lbTCorpCustInfoKeyDo.setCustNo(custNo);
        lbTCorpCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTCorpCustInfoKeyDo);
    }

    /**
     * 查询额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo 条件
     * @return List<LbTCorpCustInfoDo>
     */
    @Override
    public List<LbTCorpCustInfoDo> selectByExample(LbTCorpCustInfoQuery lbTCorpCustInfo) {
        return getMapper().selectByExample(buildExample(lbTCorpCustInfo));
    }

    /**
     * 新增额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTCorpCustInfoDo lbTCorpCustInfo) {
        if (lbTCorpCustInfo == null) {
            return -1;
        }

        lbTCorpCustInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTCorpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCorpCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTCorpCustInfo);
    }

    /**
     * 修改额度中心-中间表-对公客户信息信息
     *
     * @param lbTCorpCustInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTCorpCustInfoDo lbTCorpCustInfo) {
        if (lbTCorpCustInfo == null) {
            return -1;
        }
        lbTCorpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCorpCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTCorpCustInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTCorpCustInfoDo lbTCorpCustInfo,
        LbTCorpCustInfoQuery lbTCorpCustInfoQuery) {
        lbTCorpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTCorpCustInfo, buildExample(lbTCorpCustInfoQuery));
    }

    /**
     * 构建额度中心-中间表-对公客户信息Example信息
     *
     * @param lbTCorpCustInfo
     * @return
     */
    public LbTCorpCustInfoExample buildExample(LbTCorpCustInfoQuery lbTCorpCustInfo) {
        LbTCorpCustInfoExample example = new LbTCorpCustInfoExample();
        LbTCorpCustInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTCorpCustInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTCorpCustInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTCorpCustInfo.getCustTyp());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTCorpCustInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTCorpCustInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTCorpCustInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTCorpCustInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTCorpCustInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTCorpCustInfo.getOwnOrganId());
            }
        }
        buildExampleExt(lbTCorpCustInfo, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-对公客户信息ExampleExt方法
     *
     * @param lbTCorpCustInfo
     * @return
     */
    public void buildExampleExt(LbTCorpCustInfoQuery lbTCorpCustInfo, LbTCorpCustInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 清空额度中心-中间表-对公客户信息表数据
     *
     * @return 影响行数
     */
    @Override
    public int truncateTable() {
        return getMapper().truncateTable();
    }

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入对公客户信息数据
     *
     * @return 影响行数
     */
    @Override
    public int insertFromSource() {
        return getMapper().insertFromSource();
    }

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入对公客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    @Override
    public int insertFromSource(List<String> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return 0;
        }
        return getMapper().insertFromSourceByUserIdList(userIdList);
    }

}
