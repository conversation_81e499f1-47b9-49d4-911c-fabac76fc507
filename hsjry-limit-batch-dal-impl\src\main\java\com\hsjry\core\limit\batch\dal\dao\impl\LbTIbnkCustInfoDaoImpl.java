package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTIbnkCustInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTIbnkCustInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIbnkCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIbnkCustInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIbnkCustInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTIbnkCustInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-同业客户信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbTIbnkCustInfoDaoImpl extends AbstractBaseDaoImpl<LbTIbnkCustInfoDo, LbTIbnkCustInfoMapper>
    implements LbTIbnkCustInfoDao {
    /**
     * 分页查询
     *
     * @param lbTIbnkCustInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTIbnkCustInfoDo> selectPage(LbTIbnkCustInfoQuery lbTIbnkCustInfo, PageParam pageParam) {
        LbTIbnkCustInfoExample example = buildExample(lbTIbnkCustInfo);
        return PageHelper.<LbTIbnkCustInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-同业客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public LbTIbnkCustInfoDo selectByKey(String custNo) {
        LbTIbnkCustInfoKeyDo lbTIbnkCustInfoKeyDo = new LbTIbnkCustInfoKeyDo();
        lbTIbnkCustInfoKeyDo.setCustNo(custNo);
        lbTIbnkCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTIbnkCustInfoKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-同业客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public int deleteByKey(String custNo) {
        LbTIbnkCustInfoKeyDo lbTIbnkCustInfoKeyDo = new LbTIbnkCustInfoKeyDo();
        lbTIbnkCustInfoKeyDo.setCustNo(custNo);
        lbTIbnkCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTIbnkCustInfoKeyDo);
    }

    /**
     * 查询额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo 条件
     * @return List<LbTIbnkCustInfoDo>
     */
    @Override
    public List<LbTIbnkCustInfoDo> selectByExample(LbTIbnkCustInfoQuery lbTIbnkCustInfo) {
        return getMapper().selectByExample(buildExample(lbTIbnkCustInfo));
    }

    /**
     * 新增额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTIbnkCustInfoDo lbTIbnkCustInfo) {
        if (lbTIbnkCustInfo == null) {
            return -1;
        }

        lbTIbnkCustInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTIbnkCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTIbnkCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTIbnkCustInfo);
    }

    /**
     * 修改额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTIbnkCustInfoDo lbTIbnkCustInfo) {
        if (lbTIbnkCustInfo == null) {
            return -1;
        }
        lbTIbnkCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTIbnkCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTIbnkCustInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTIbnkCustInfoDo lbTIbnkCustInfo,
        LbTIbnkCustInfoQuery lbTIbnkCustInfoQuery) {
        lbTIbnkCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTIbnkCustInfo, buildExample(lbTIbnkCustInfoQuery));
    }

    /**
     * 构建额度中心-中间表-同业客户信息Example信息
     *
     * @param lbTIbnkCustInfo
     * @return
     */
    public LbTIbnkCustInfoExample buildExample(LbTIbnkCustInfoQuery lbTIbnkCustInfo) {
        LbTIbnkCustInfoExample example = new LbTIbnkCustInfoExample();
        LbTIbnkCustInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTIbnkCustInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTIbnkCustInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTIbnkCustInfo.getCustTyp());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTIbnkCustInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTIbnkCustInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTIbnkCustInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTIbnkCustInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTIbnkCustInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTIbnkCustInfo.getOwnOrganId());
            }
        }
        buildExampleExt(lbTIbnkCustInfo, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-同业客户信息ExampleExt方法
     *
     * @param lbTIbnkCustInfo
     * @return
     */
    public void buildExampleExt(LbTIbnkCustInfoQuery lbTIbnkCustInfo, LbTIbnkCustInfoExample.Criteria criteria) {

        //自定义实现
    }

    @Override
    public int truncateTable() {
        return getMapper().truncateTable();
    }

    @Override
    public int insertFromSource() {
        return getMapper().insertFromSource();
    }

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入同业客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    @Override
    public int insertFromSource(List<String> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return 0;
        }
        return getMapper().insertFromSourceByUserIdList(userIdList);
    }
}
