/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.convert.file.LbHOlLoanInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHOlLoanInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHOlLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHOlLoanInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 网贷历史借据信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbHOlLoanInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbHOlLoanInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHOlLoanInfoData> {

    // CSV文件字段列索引常量（按LB_H_OL_LOAN_INFO表的字段顺序）
    /** 资产借据编号列数 */
    private static final int LOAN_INVOICE_ID_NUM = 1;
    /** 支用申请编号列数 */
    private static final int LOAN_APPLY_ID_NUM = 2;
    /** 用户编号列数 */
    private static final int USER_ID_NUM = 3;
    /** 用户姓名列数 */
    private static final int USER_NAME_NUM = 4;
    /** 借款金额列数 */
    private static final int LOAN_AMOUNT_NUM = 5;
    /** 借款利率列数 */
    private static final int RATE_NUM = 6;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 7;
    /** 产品名称列数 */
    private static final int PRODUCT_NAME_NUM = 8;
    /** 产品种类列数 */
    private static final int PRODUCT_CATALOG_NUM = 9;
    /** 借款起始时间列数 */
    private static final int LOAN_START_TIME_NUM = 10;
    /** 借款到期时间列数 */
    private static final int LOAN_END_TIME_NUM = 11;
    /** 借据状态列数 */
    private static final int STATUS_NUM = 12;
    /** 放款方式列数 */
    private static final int LOAN_TYPE_NUM = 13;
    /** 商户编号列数 */
    private static final int MERCHANT_ID_NUM = 14;
    /** 商户名称列数 */
    private static final int MERCHANT_NAME_NUM = 15;
    /** 门店id列数 */
    private static final int STORE_ID_NUM = 16;
    /** 门店名称列数 */
    private static final int STORE_NAME_NUM = 17;
    /** 还款日列数 */
    private static final int REPAY_DAY_NUM = 18;
    /** 渠道编号列数 */
    private static final int CHANNEL_NO_NUM = 19;
    /** 分期金额列数 */
    private static final int INSTALLMENT_AMOUNT_NUM = 20;
    /** 分期期数列数 */
    private static final int INSTALLMENT_NUM_NUM = 21;
    /** 放款时间列数 */
    private static final int LOAN_PAY_TIME_NUM = 22;
    /** 营销中心编号列数 */
    private static final int MARKET_CENTER_ID_NUM = 23;
    /** 结清日期列数 */
    private static final int SETTLE_DATE_NUM = 24;
    /** 操作者编号列数 */
    private static final int OPERATOR_ID_NUM = 25;
    /** 所属组织id列数 */
    private static final int OWN_ORGAN_ID_NUM = 26;
    /** 租户号列数 */
    private static final int TENANT_ID_NUM = 27;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 28;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 29;
    /** 客户经理编号列数 */
    private static final int CUST_MGR_ID_NUM = 30;
    /** 客户手机号码列数 */
    private static final int USER_TEL_NUM = 31;
    /** 客户证件类型列数 */
    private static final int CERTIFICATE_TYPE_NUM = 32;
    /** 客户证件号码列数 */
    private static final int CERTIFICATE_NO_NUM = 33;
    /** 代扣协议编号列数 */
    private static final int WITHHOLD_PROTOCOL_ID_NUM = 34;
    /** 业务标识列数 */
    private static final int BUSINESS_SIGN_NUM = 35;
    /** 贷款分类列数 */
    private static final int CLASSIFICATION_NUM = 36;
    /** 合同编号列数 */
    private static final int CONTRACT_ID_NUM = 37;
    /** 授信申请编号列数 */
    private static final int CREDIT_APPLY_ID_NUM = 38;
    /** 客户经理机构编号列数 */
    private static final int CUST_MGR_ORGAN_ID_NUM = 39;
    /** 数据时间列数（历史表特有） */
    private static final int DATA_DATE_NUM = 40;

    /** 最小字段数量（历史表40个字段） */
    private static final int MIN_FIELD_COUNT = 40;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHOlLoanInfoDao lbHOlLoanInfoDao;
    @Value("${project.h.ol_loan_info.filename:h_ol_loan_info_[DATE].csv}")
    private String fileName;
    @Value("${project.h.ol_loan_info.remoteFilePath:/hsdata/logs/dev/loan4-0-hnnx/file/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHOlLoanInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        Integer businessDate = jobShared.getBusinessDate();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        Integer batchNum = jobShared.getBatchNum();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s],当前分片:[%s]",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo, batchNum);

        ShardingResult<LbHOlLoanInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);

        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取历史借据信息文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst());

            // 使用并行流处理数据，提升性能
            List<LbHOlLoanInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取历史借据信息文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "历史借据信息文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHOlLoanInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHOlLoanInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "历史借据信息数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "历史借据信息数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        // 设置数据日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则删除当前业务日期的数据
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,删除目标表 lb_h_ol_loan_info 中 DATA_DATE 为 " + businessDate + " 的数据");
            lbHOlLoanInfoDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null，设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbHOlLoanInfoDo> insertList = dataList.parallelStream()
            .map(LbHOlLoanInfoConverter::data2Do)
            .filter(this::validateData)
            .collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条历史借据信息数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_OL_LOAN_INFO;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null，设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]历史借据信息文件处理", jobTradeDesc);

        // 从本地读取文件，本地读取文件路径为/hsdata/logs/dev/loan4-0-hnnx/file/
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在，尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在，请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        return sharedList;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 是否跳过文件第一行
     * @return true-跳过第一行（标题行），false-不跳过
     */
    private boolean skipFirst() {
        return true;
    }



    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHOlLoanInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbHOlLoanInfoData> fileDataList = originData.parallelStream()
            .filter(Objects::nonNull)
            .filter(item -> !StringUtil.isBlank(item))
            .map(item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount))
            .filter(Objects::nonNull)
            .collect(Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 提取单行数据解析逻辑，提高代码复用性和可维护性
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHOlLoanInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbHOlLoanInfoData fileData = new LbHOlLoanInfoData();

        // 设置字符串字段
        fileData.setLoanInvoiceId(getFieldValue(split, LOAN_INVOICE_ID_NUM - 1));
        fileData.setLoanApplyId(getFieldValue(split, LOAN_APPLY_ID_NUM - 1));
        fileData.setUserId(getFieldValue(split, USER_ID_NUM - 1));
        fileData.setUserName(getFieldValue(split, USER_NAME_NUM - 1));
        fileData.setProductId(getFieldValue(split, PRODUCT_ID_NUM - 1));
        fileData.setProductName(getFieldValue(split, PRODUCT_NAME_NUM - 1));
        fileData.setProductCatalog(getFieldValue(split, PRODUCT_CATALOG_NUM - 1));
        fileData.setLoanStartTime(getFieldValue(split, LOAN_START_TIME_NUM - 1));
        fileData.setLoanEndTime(getFieldValue(split, LOAN_END_TIME_NUM - 1));
        fileData.setStatus(getFieldValue(split, STATUS_NUM - 1));
        fileData.setLoanType(getFieldValue(split, LOAN_TYPE_NUM - 1));
        fileData.setMerchantId(getFieldValue(split, MERCHANT_ID_NUM - 1));
        fileData.setMerchantName(getFieldValue(split, MERCHANT_NAME_NUM - 1));
        fileData.setStoreId(getFieldValue(split, STORE_ID_NUM - 1));
        fileData.setStoreName(getFieldValue(split, STORE_NAME_NUM - 1));
        fileData.setChannelNo(getFieldValue(split, CHANNEL_NO_NUM - 1));
        fileData.setLoanPayTime(getFieldValue(split, LOAN_PAY_TIME_NUM - 1));
        fileData.setMarketCenterId(getFieldValue(split, MARKET_CENTER_ID_NUM - 1));
        fileData.setSettleDate(getFieldValue(split, SETTLE_DATE_NUM - 1));
        fileData.setOperatorId(getFieldValue(split, OPERATOR_ID_NUM - 1));
        fileData.setOwnOrganId(getFieldValue(split, OWN_ORGAN_ID_NUM - 1));
        fileData.setTenantId(getFieldValue(split, TENANT_ID_NUM - 1));
        fileData.setCreateTime(getFieldValue(split, CREATE_TIME_NUM - 1));
        fileData.setUpdateTime(getFieldValue(split, UPDATE_TIME_NUM - 1));
        fileData.setCustMgrId(getFieldValue(split, CUST_MGR_ID_NUM - 1));
        fileData.setUserTel(getFieldValue(split, USER_TEL_NUM - 1));
        fileData.setCertificateType(getFieldValue(split, CERTIFICATE_TYPE_NUM - 1));
        fileData.setCertificateNo(getFieldValue(split, CERTIFICATE_NO_NUM - 1));
        fileData.setWithholdProtocolId(getFieldValue(split, WITHHOLD_PROTOCOL_ID_NUM - 1));
        fileData.setBusinessSign(getFieldValue(split, BUSINESS_SIGN_NUM - 1));
        fileData.setClassification(getFieldValue(split, CLASSIFICATION_NUM - 1));
        fileData.setContractId(getFieldValue(split, CONTRACT_ID_NUM - 1));
        fileData.setCreditApplyId(getFieldValue(split, CREDIT_APPLY_ID_NUM - 1));
        fileData.setCustMgrOrganId(getFieldValue(split, CUST_MGR_ORGAN_ID_NUM - 1));
        // 历史表特有字段
        fileData.setDataDate(getFieldValue(split, DATA_DATE_NUM - 1));

        // 安全解析数字字段
        fileData.setLoanAmount(parseBigDecimalSafely(getFieldValue(split, LOAN_AMOUNT_NUM - 1), parseErrorCount));
        fileData.setRate(parseBigDecimalSafely(getFieldValue(split, RATE_NUM - 1), parseErrorCount));
        fileData.setRepayDay(parseIntegerSafely(getFieldValue(split, REPAY_DAY_NUM - 1), parseErrorCount));
        fileData.setInstallmentAmount(parseBigDecimalSafely(getFieldValue(split, INSTALLMENT_AMOUNT_NUM - 1), parseErrorCount));
        fileData.setInstallmentNum(parseIntegerSafely(getFieldValue(split, INSTALLMENT_NUM_NUM - 1), parseErrorCount));

        return fileData;
    }

    /**
     * 安全获取字段值
     * 避免数组越界异常
     *
     * @param split 分割后的数组
     * @param index 索引
     * @return 字段值
     */
    private String getFieldValue(String[] split, int index) {
        if (index < 0 || index >= split.length) {
            return null;
        }
        String value = split[index];
        return StringUtil.isBlank(value) ? null : value.trim();
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全解析Integer
     * 统一的整数字段解析逻辑
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的Integer值
     */
    private Integer parseIntegerSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? 0 : Integer.valueOf(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return 0;
        }
    }

    /**
     * 增强的数据验证方法
     * 验证必要字段，确保数据质量
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbHOlLoanInfoDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        // 验证必要字段
        return StringUtil.isNotBlank(data.getLoanApplyId())
            && StringUtil.isNotBlank(data.getTenantId())
            && StringUtil.isNotBlank(data.getDataDate());
    }

    /**
     * 批量插入数据
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param doList 待插入的数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHOlLoanInfoDo> doList, String prefixLog) {
        if (CollectionUtil.isEmpty(doList)) {
            return;
        }

        int totalSize = doList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入历史借据信息数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]",
            totalSize, batchCount, BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHOlLoanInfoDo> batchList = doList.subList(fromIndex, toIndex);

            try {
                lbHOlLoanInfoDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }
}
