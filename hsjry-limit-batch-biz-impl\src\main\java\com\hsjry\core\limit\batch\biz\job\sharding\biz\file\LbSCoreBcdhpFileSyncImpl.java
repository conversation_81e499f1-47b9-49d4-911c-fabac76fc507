/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.convert.file.LbSCoreBcdhpConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbSCoreBcdhpData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSCoreBcdhpDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBcdhpDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 银行承兑汇票文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:00
 */
@Slf4j
@Service("lbSCoreBcdhpFileSyncImpl")
@RequiredArgsConstructor
public class LbSCoreBcdhpFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbSCoreBcdhpData> {

    /** 法人代码列数 */
    private static final int FAREDM_NUM = 1;
    /** 银承协议编号列数 */
    private static final int CDXYBH_NUM = 2;
    /** 本次出票批次列数 */
    private static final int BCCPPC_NUM = 3;
    /** 本次出票编号列数 */
    private static final int CHUPBH_NUM = 4;
    /** 签发机构号列数 */
    private static final int YNGYJG_NUM = 5;
    /** 申请机构号列数 */
    private static final int ZHNGJG_NUM = 6;
    /** 银承合同编号列数 */
    private static final int HTNGBH_NUM = 7;
    /** 协议总票面金额列数 */
    private static final int ZONGJE_NUM = 8;
    /** 凭证种类列数 */
    private static final int PNGZZL_NUM = 9;
    /** 票据轮冠字列数 */
    private static final int PJLUGZ_NUM = 10;
    /** 票据号码列数 */
    private static final int PIOJHM_NUM = 11;
    /** 币种列数 */
    private static final int HUOBDH_NUM = 12;
    /** 票面金额列数 */
    private static final int PIOMJE_NUM = 13;
    /** 汇票金额列数 */
    private static final int HUIPJE_NUM = 14;
    /** 备款金额列数 */
    private static final int BEIKJE_NUM = 15;
    /** 签发行联行行号列数 */
    private static final int QFHLHH_NUM = 16;
    /** 签发行联行行名列数 */
    private static final int QFHLHM_NUM = 17;
    /** 出票人客户号列数 */
    private static final int KEHHAO_NUM = 18;
    /** 出票人帐号列数 */
    private static final int CHPRZH_NUM = 19;
    /** 出票人全称列数 */
    private static final int CHPRQC_NUM = 20;
    /** 收款人客户号列数 */
    private static final int SKRKHH_NUM = 21;
    /** 收款人帐号列数 */
    private static final int SHKRZH_NUM = 22;
    /** 收款人户名列数 */
    private static final int SHKRXM_NUM = 23;
    /** 收款行行号列数 */
    private static final int SKHHAO_NUM = 24;
    /** 收款行行名列数 */
    private static final int SHKHHM_NUM = 25;
    /** 是否全额保证金列数 */
    private static final int SHFOBZ_NUM = 26;
    /** 额度层次列数 */
    private static final int EDCENG_NUM = 27;
    /** 银承汇票签发方式列数 */
    private static final int YCQFFS_NUM = 28;
    /** 交易日期列数 */
    private static final int JIOYRQ_NUM = 29;
    /** 到期日期列数 */
    private static final int DAOQRQ_NUM = 30;
    /** 签发日期列数 */
    private static final int QNFARQ_NUM = 31;
    /** 承兑日期列数 */
    private static final int CDUIRQ_NUM = 32;
    /** 备款日期列数 */
    private static final int RUZHRQ_NUM = 33;
    /** 垫款标志列数 */
    private static final int DNKNBZ_NUM = 34;
    /** 垫款借据编号列数 */
    private static final int JIEJUH_NUM = 35;
    /** 垫款金额列数 */
    private static final int ZHDKJE_NUM = 36;
    /** 资金去向列数 */
    private static final int ZIJNQX_NUM = 37;
    /** 资金转入账号列数 */
    private static final int DCZRZH_NUM = 38;
    /** 持票人账号列数 */
    private static final int SKZHAO_NUM = 39;
    /** 持票人名称列数 */
    private static final int CHIPMC_NUM = 40;
    /** 持票人开户行行号列数 */
    private static final int KAIHHH_NUM = 41;
    /** 持票人开户行行名列数 */
    private static final int KAIHHM_NUM = 42;
    /** 未用退回日期列数 */
    private static final int SXIORQ_NUM = 43;
    /** 五级分类日期列数 */
    private static final int WJFLRQ_NUM = 44;
    /** 五级贷款分类列数 */
    private static final int WJDKFL_NUM = 45;
    /** 承兑汇票状态列数 */
    private static final int YCHPZT_NUM = 46;
    /** 票据状态列数 */
    private static final int PIOJZT_NUM = 47;
    /** 是否电子票据列数 */
    private static final int SFDZPJ_NUM = 48;
    /** 明细序号列数 */
    private static final int MXXHAO_NUM = 49;
    /** 挂失日期列数 */
    private static final int GSRIQI_NUM = 50;
    /** 解挂日期列数 */
    private static final int JGRIQI_NUM = 51;
    /** 备注列数 */
    private static final int REMARK_NUM = 52;
    /** 签发行联行地址列数 */
    private static final int BEIZXX_NUM = 53;
    /** 开户机构列数 */
    private static final int KAIHJG_NUM = 54;
    /** 开户柜员列数 */
    private static final int KAIHGY_NUM = 55;
    /** 维护柜员列数 */
    private static final int WEIHGY_NUM = 56;
    /** 维护日期列数 */
    private static final int WEIHRQ_NUM = 57;
    /** 销户柜员列数 */
    private static final int XIOHGY_NUM = 58;
    /** 销户日期列数 */
    private static final int XIOHRQ_NUM = 59;
    /** 维护机构列数 */
    private static final int WEIHJG_NUM = 60;
    /** 维护时间列数 */
    private static final int WEIHSJ_NUM = 61;
    /** 记录状态列数 */
    private static final int SHJNCH_NUM = 62;
    /** 记录状态列数 */
    private static final int JILUZT_NUM = 63;

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 63;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbSCoreBcdhpDao lbSCoreBcdhpDao;
    @Value("${project.core.bcdhp.filename:CBS_BCDHP_[DATE].csv}")
    private String fileName;
    @Value("${project.core.bcdhp.remoteFilePath:/hsdata/logs/dev/loan4-0-hnnx/file/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbSCoreBcdhpData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LbSCoreBcdhpData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        shardingResult.setJobShared(jobShared);
        shardingResult.setLcSliceBatchSerialDo(lcSliceBatchSerialDo);

        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]读取处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        log.info(prefixLog + "文件分片处理开始");

        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst());

            // 使用并行流处理数据，提升性能
            List<LbSCoreBcdhpData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSCoreBcdhpData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbSCoreBcdhpData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "银承汇票数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "银承汇票数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_s_core_bcdhp");
            lbSCoreBcdhpDao.deleteAll();
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null，设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbSCoreBcdhpDo> insertList = dataList.parallelStream().map(LbSCoreBcdhpConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条银承汇票数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.S_CORE_BCDHP_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null，设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在，尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在，请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        return sharedList;
    }



    /**
     * 是否跳过第一行
     *
     * @return 是否跳过
     */
    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbSCoreBcdhpData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbSCoreBcdhpData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 根据银承汇票表结构解析所有字段
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbSCoreBcdhpData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbSCoreBcdhpData fileData = new LbSCoreBcdhpData();

        // 设置字符串字段
        fileData.setFaredm(split[FAREDM_NUM - 1]);
        fileData.setCdxybh(split[CDXYBH_NUM - 1]);
        fileData.setChupbh(split[CHUPBH_NUM - 1]);
        fileData.setYngyjg(split[YNGYJG_NUM - 1]);
        fileData.setZhngjg(split[ZHNGJG_NUM - 1]);
        fileData.setHtngbh(split[HTNGBH_NUM - 1]);
        fileData.setPngzzl(split[PNGZZL_NUM - 1]);
        fileData.setPjlugz(split[PJLUGZ_NUM - 1]);
        fileData.setPiojhm(split[PIOJHM_NUM - 1]);
        fileData.setHuobdh(split[HUOBDH_NUM - 1]);
        fileData.setQfhlhh(split[QFHLHH_NUM - 1]);
        fileData.setQfhlhm(split[QFHLHM_NUM - 1]);
        fileData.setKehhao(split[KEHHAO_NUM - 1]);
        fileData.setChprzh(split[CHPRZH_NUM - 1]);
        fileData.setChprqc(split[CHPRQC_NUM - 1]);
        fileData.setSkrkhh(split[SKRKHH_NUM - 1]);
        fileData.setShkrzh(split[SHKRZH_NUM - 1]);
        fileData.setShkrxm(split[SHKRXM_NUM - 1]);
        fileData.setSkhhao(split[SKHHAO_NUM - 1]);
        fileData.setShkhhm(split[SHKHHM_NUM - 1]);
        fileData.setShfobz(split[SHFOBZ_NUM - 1]);
        fileData.setEdceng(split[EDCENG_NUM - 1]);
        fileData.setYcqffs(split[YCQFFS_NUM - 1]);
        fileData.setJioyrq(split[JIOYRQ_NUM - 1]);
        fileData.setDaoqrq(split[DAOQRQ_NUM - 1]);
        fileData.setQnfarq(split[QNFARQ_NUM - 1]);
        fileData.setCduirq(split[CDUIRQ_NUM - 1]);
        fileData.setRuzhrq(split[RUZHRQ_NUM - 1]);
        fileData.setDnknbz(split[DNKNBZ_NUM - 1]);
        fileData.setJiejuh(split[JIEJUH_NUM - 1]);
        fileData.setZijnqx(split[ZIJNQX_NUM - 1]);
        fileData.setDczrzh(split[DCZRZH_NUM - 1]);
        fileData.setSkzhao(split[SKZHAO_NUM - 1]);
        fileData.setChipmc(split[CHIPMC_NUM - 1]);
        fileData.setKaihhh(split[KAIHHH_NUM - 1]);
        fileData.setKaihhm(split[KAIHHM_NUM - 1]);
        fileData.setSxiorq(split[SXIORQ_NUM - 1]);
        fileData.setWjflrq(split[WJFLRQ_NUM - 1]);
        fileData.setWjdkfl(split[WJDKFL_NUM - 1]);
        fileData.setYchpzt(split[YCHPZT_NUM - 1]);
        fileData.setPiojzt(split[PIOJZT_NUM - 1]);
        fileData.setSfdzpj(split[SFDZPJ_NUM - 1]);
        fileData.setGsriqi(split[GSRIQI_NUM - 1]);
        fileData.setJgriqi(split[JGRIQI_NUM - 1]);
        fileData.setRemark(split[REMARK_NUM - 1]);
        fileData.setBeizxx(split[BEIZXX_NUM - 1]);
        fileData.setKaihjg(split[KAIHJG_NUM - 1]);
        fileData.setKaihgy(split[KAIHGY_NUM - 1]);
        fileData.setWeihgy(split[WEIHGY_NUM - 1]);
        fileData.setWeihrq(split[WEIHRQ_NUM - 1]);
        fileData.setXiohgy(split[XIOHGY_NUM - 1]);
        fileData.setXiohrq(split[XIOHRQ_NUM - 1]);
        fileData.setWeihjg(split[WEIHJG_NUM - 1]);
        fileData.setWeihsj(split[WEIHSJ_NUM - 1]);
        fileData.setShjnch(split[SHJNCH_NUM - 1]);
        fileData.setJiluzt(split[JILUZT_NUM - 1]);

        // 安全解析数字字段
        fileData.setBccppc(parseBigDecimalSafely(split[BCCPPC_NUM - 1], parseErrorCount));
        fileData.setZongje(parseBigDecimalSafely(split[ZONGJE_NUM - 1], parseErrorCount));
        fileData.setPiomje(parseBigDecimalSafely(split[PIOMJE_NUM - 1], parseErrorCount));
        fileData.setHuipje(parseBigDecimalSafely(split[HUIPJE_NUM - 1], parseErrorCount));
        fileData.setBeikje(parseBigDecimalSafely(split[BEIKJE_NUM - 1], parseErrorCount));
        fileData.setZhdkje(parseBigDecimalSafely(split[ZHDKJE_NUM - 1], parseErrorCount));
        fileData.setMxxhao(parseBigDecimalSafely(split[MXXHAO_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 增强的数据验证方法
     * 使用Objects工具类和优化的验证逻辑
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbSCoreBcdhpDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getFaredm())) {
            log.warn("法人代码为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getCdxybh())) {
            log.warn("银承协议编号为空,数据无效");
            return false;
        }

        if (Objects.isNull(data.getBccppc())) {
            log.warn("本次出票批次为空,银承协议编号:[{}]", data.getCdxybh());
            return false;
        }

        if (StringUtil.isBlank(data.getChupbh())) {
            log.warn("本次出票编号为空,银承协议编号:[{}]", data.getCdxybh());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbSCoreBcdhpDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbSCoreBcdhpDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbSCoreBcdhpDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }
}
