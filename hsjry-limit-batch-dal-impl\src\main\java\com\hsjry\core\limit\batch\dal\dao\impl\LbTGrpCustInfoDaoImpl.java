package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTGrpCustInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTGrpCustInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTGrpCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTGrpCustInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTGrpCustInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTGrpCustInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-集团客户信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbTGrpCustInfoDaoImpl extends AbstractBaseDaoImpl<LbTGrpCustInfoDo, LbTGrpCustInfoMapper>
    implements LbTGrpCustInfoDao {
    /**
     * 分页查询
     *
     * @param lbTGrpCustInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTGrpCustInfoDo> selectPage(LbTGrpCustInfoQuery lbTGrpCustInfo, PageParam pageParam) {
        LbTGrpCustInfoExample example = buildExample(lbTGrpCustInfo);
        return PageHelper.<LbTGrpCustInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-集团客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public LbTGrpCustInfoDo selectByKey(String custNo) {
        LbTGrpCustInfoKeyDo lbTGrpCustInfoKeyDo = new LbTGrpCustInfoKeyDo();
        lbTGrpCustInfoKeyDo.setCustNo(custNo);
        lbTGrpCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTGrpCustInfoKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-集团客户信息
     *
     * @param custNo
     * @return
     */
    @Override
    public int deleteByKey(String custNo) {
        LbTGrpCustInfoKeyDo lbTGrpCustInfoKeyDo = new LbTGrpCustInfoKeyDo();
        lbTGrpCustInfoKeyDo.setCustNo(custNo);
        lbTGrpCustInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTGrpCustInfoKeyDo);
    }

    /**
     * 查询额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo 条件
     * @return List<LbTGrpCustInfoDo>
     */
    @Override
    public List<LbTGrpCustInfoDo> selectByExample(LbTGrpCustInfoQuery lbTGrpCustInfo) {
        return getMapper().selectByExample(buildExample(lbTGrpCustInfo));
    }

    /**
     * 新增额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTGrpCustInfoDo lbTGrpCustInfo) {
        if (lbTGrpCustInfo == null) {
            return -1;
        }

        lbTGrpCustInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTGrpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTGrpCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTGrpCustInfo);
    }

    /**
     * 修改额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTGrpCustInfoDo lbTGrpCustInfo) {
        if (lbTGrpCustInfo == null) {
            return -1;
        }
        lbTGrpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTGrpCustInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTGrpCustInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTGrpCustInfoDo lbTGrpCustInfo, LbTGrpCustInfoQuery lbTGrpCustInfoQuery) {
        lbTGrpCustInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTGrpCustInfo, buildExample(lbTGrpCustInfoQuery));
    }

    /**
     * 构建额度中心-中间表-集团客户信息Example信息
     *
     * @param lbTGrpCustInfo
     * @return
     */
    public LbTGrpCustInfoExample buildExample(LbTGrpCustInfoQuery lbTGrpCustInfo) {
        LbTGrpCustInfoExample example = new LbTGrpCustInfoExample();
        LbTGrpCustInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTGrpCustInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTGrpCustInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTGrpCustInfo.getCustTyp());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTGrpCustInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTGrpCustInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTGrpCustInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTGrpCustInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTGrpCustInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTGrpCustInfo.getOwnOrganId());
            }
        }
        buildExampleExt(lbTGrpCustInfo, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-集团客户信息ExampleExt方法
     *
     * @param lbTGrpCustInfo
     * @return
     */
    public void buildExampleExt(LbTGrpCustInfoQuery lbTGrpCustInfo, LbTGrpCustInfoExample.Criteria criteria) {

    }

    /**
     * 清空额度中心-中间表-集团客户信息表数据
     *
     * @return 影响行数
     */
    @Override
    public int truncateTable() {
        return getMapper().truncateTable();
    }

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @return 影响行数
     */
    @Override
    public int insertFromSource() {
        return getMapper().insertFromSource();
    }

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    @Override
    public int insertFromSource(List<String> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return 0;
        }
        return getMapper().insertFromSourceByUserIdList(userIdList);
    }
}
