package com.hsjry.core.limit.batch.dal.dao.impl;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreAdkmxDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreAdkmxMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkmxDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkmxKeyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkmxExample;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAdkmxQuery;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Repository
public class LbHCoreAdkmxDaoImpl extends AbstractBaseDaoImpl<LbHCoreAdkmxDo, LbHCoreAdkmxMapper>
    implements LbHCoreAdkmxDao {
    /**
     * 分页查询
     *
     * @param lbHCoreAdkmx 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCoreAdkmxDo> selectPage(LbHCoreAdkmxQuery lbHCoreAdkmx, PageParam pageParam) {
        LbHCoreAdkmxExample example = buildExample(lbHCoreAdkmx);
        return PageHelper.<LbHCoreAdkmxDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @param dataDate
     * @return
     */
    @Override
    public LbHCoreAdkmxDo selectByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao, String dataDate) {
        LbHCoreAdkmxKeyDo lbHCoreAdkmxKeyDo = new LbHCoreAdkmxKeyDo();
        lbHCoreAdkmxKeyDo.setFaredm(faredm);
        lbHCoreAdkmxKeyDo.setDkjeju(dkjeju);
        lbHCoreAdkmxKeyDo.setMxxhao(mxxhao);
        lbHCoreAdkmxKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHCoreAdkmxKeyDo);
    }

    /**
     * 根据key删除核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao, String dataDate) {
        LbHCoreAdkmxKeyDo lbHCoreAdkmxKeyDo = new LbHCoreAdkmxKeyDo();
        lbHCoreAdkmxKeyDo.setFaredm(faredm);
        lbHCoreAdkmxKeyDo.setDkjeju(dkjeju);
        lbHCoreAdkmxKeyDo.setMxxhao(mxxhao);
        lbHCoreAdkmxKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHCoreAdkmxKeyDo);
    }

    /**
     * 查询核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx 条件
     * @return List<LbHCoreAdkmxDo>
     */
    @Override
    public List<LbHCoreAdkmxDo> selectByExample(LbHCoreAdkmxQuery lbHCoreAdkmx) {
        return getMapper().selectByExample(buildExample(lbHCoreAdkmx));
    }

    /**
     * 新增核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCoreAdkmxDo lbHCoreAdkmx) {
        if (lbHCoreAdkmx == null) {
            return -1;
        }

        return getMapper().insertSelective(lbHCoreAdkmx);
    }

    /**
     * 修改核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx
     * @return
     */
    @Override
    public int updateBySelective(LbHCoreAdkmxDo lbHCoreAdkmx) {
        if (lbHCoreAdkmx == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbHCoreAdkmx);
    }

    @Override
    public int updateBySelectiveByExample(LbHCoreAdkmxDo lbHCoreAdkmx, LbHCoreAdkmxQuery lbHCoreAdkmxQuery) {
        return getMapper().updateByExampleSelective(lbHCoreAdkmx, buildExample(lbHCoreAdkmxQuery));
    }

    /**
     * 构建核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）Example信息
     *
     * @param lbHCoreAdkmx
     * @return
     */
    public LbHCoreAdkmxExample buildExample(LbHCoreAdkmxQuery lbHCoreAdkmx) {
        LbHCoreAdkmxExample example = new LbHCoreAdkmxExample();
        LbHCoreAdkmxExample.Criteria criteria = example.createCriteria();
        if (lbHCoreAdkmx != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getYngyls())) {
                criteria.andYngylsEqualTo(lbHCoreAdkmx.getYngyls());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJioyls())) {
                criteria.andJioylsEqualTo(lbHCoreAdkmx.getJioyls());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJoyisj())) {
                criteria.andJoyisjEqualTo(lbHCoreAdkmx.getJoyisj());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getShijsm())) {
                criteria.andShijsmEqualTo(lbHCoreAdkmx.getShijsm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJiaoym())) {
                criteria.andJiaoymEqualTo(lbHCoreAdkmx.getJiaoym());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getZhyonr())) {
                criteria.andZhyonrEqualTo(lbHCoreAdkmx.getZhyonr());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getChzhbz())) {
                criteria.andChzhbzEqualTo(lbHCoreAdkmx.getChzhbz());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getBchzbz())) {
                criteria.andBchzbzEqualTo(lbHCoreAdkmx.getBchzbz());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getYnjyrq())) {
                criteria.andYnjyrqEqualTo(lbHCoreAdkmx.getYnjyrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJioygy())) {
                criteria.andJioygyEqualTo(lbHCoreAdkmx.getJioygy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getBeizhu())) {
                criteria.andBeizhuEqualTo(lbHCoreAdkmx.getBeizhu());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbHCoreAdkmx.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbHCoreAdkmx.getWeihjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbHCoreAdkmx.getWeihrq());
            }
            if (null != lbHCoreAdkmx.getWeihsj()) {
                criteria.andWeihsjEqualTo(lbHCoreAdkmx.getWeihsj());
            }
            if (null != lbHCoreAdkmx.getShjnch()) {
                criteria.andShjnchEqualTo(lbHCoreAdkmx.getShjnch());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJiluzt())) {
                criteria.andJiluztEqualTo(lbHCoreAdkmx.getJiluzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCoreAdkmx.getDataDate());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getHuobdh())) {
                criteria.andHuobdhEqualTo(lbHCoreAdkmx.getHuobdh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getDaikzh())) {
                criteria.andDaikzhEqualTo(lbHCoreAdkmx.getDaikzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getDkjeju())) {
                criteria.andDkjejuEqualTo(lbHCoreAdkmx.getDkjeju());
            }
            if (null != lbHCoreAdkmx.getMxxhao()) {
                criteria.andMxxhaoEqualTo(lbHCoreAdkmx.getMxxhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getYngyjg())) {
                criteria.andYngyjgEqualTo(lbHCoreAdkmx.getYngyjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getChapdm())) {
                criteria.andChapdmEqualTo(lbHCoreAdkmx.getChapdm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getDkkjlb())) {
                criteria.andDkkjlbEqualTo(lbHCoreAdkmx.getDkkjlb());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getKehhao())) {
                criteria.andKehhaoEqualTo(lbHCoreAdkmx.getKehhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getKehzwm())) {
                criteria.andKehzwmEqualTo(lbHCoreAdkmx.getKehzwm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getFaredm())) {
                criteria.andFaredmEqualTo(lbHCoreAdkmx.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJioyrq())) {
                criteria.andJioyrqEqualTo(lbHCoreAdkmx.getJioyrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJioyfx())) {
                criteria.andJioyfxEqualTo(lbHCoreAdkmx.getJioyfx());
            }
            if (null != lbHCoreAdkmx.getJioyje()) {
                criteria.andJioyjeEqualTo(lbHCoreAdkmx.getJioyje());
            }
            if (null != lbHCoreAdkmx.getZhhuye()) {
                criteria.andZhhuyeEqualTo(lbHCoreAdkmx.getZhhuye());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getZidmch())) {
                criteria.andZidmchEqualTo(lbHCoreAdkmx.getZidmch());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getZidshm())) {
                criteria.andZidshmEqualTo(lbHCoreAdkmx.getZidshm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAdkmx.getJioyjg())) {
                criteria.andJioyjgEqualTo(lbHCoreAdkmx.getJioyjg());
            }
        }
        buildExampleExt(lbHCoreAdkmx, criteria);
        return example;
    }

    /**
     * 构建核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）ExampleExt方法
     *
     * @param lbHCoreAdkmx
     * @return
     */
    public void buildExampleExt(LbHCoreAdkmxQuery lbHCoreAdkmx, LbHCoreAdkmxExample.Criteria criteria) {

        //自定义实现
    }

    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }

}
