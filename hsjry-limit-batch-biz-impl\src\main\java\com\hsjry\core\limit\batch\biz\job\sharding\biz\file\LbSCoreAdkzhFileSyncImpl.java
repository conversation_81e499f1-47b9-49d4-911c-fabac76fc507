/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.convert.file.LbSCoreAdkzhConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbSCoreAdkzhData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSCoreAdkzhDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAdkzhDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 核心系统-落地表-贷款账户主表文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 11:00
 */
@Slf4j
@Service("lbSCoreAdkzhFileSyncImpl")
@RequiredArgsConstructor
public class LbSCoreAdkzhFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbSCoreAdkzhData> {

    /** 根据LB_S_CORE_ADKZH表结构定义字段位置常量 */
    private static final int FAREDM_NUM = 1;    // 法人代码
    private static final int DAIKZH_NUM = 2;    // 贷款账号
    private static final int DKJEJU_NUM = 3;    // 贷款借据号
    private static final int HTNGBH_NUM = 4;    // 合同编号
    private static final int KEHHAO_NUM = 5;    // 客户号
    private static final int KEHZWM_NUM = 6;    // 客户名
    private static final int YNGYJG_NUM = 7;    // 营业机构
    private static final int ZHNGJG_NUM = 8;    // 账务机构
    private static final int CHAPDM_NUM = 9;    // 产品代码
    private static final int CHAPMC_NUM = 10;   // 产品名称
    private static final int DKKJLB_NUM = 11;   // 会计类别
    private static final int KAIHRQ_NUM = 12;   // 开户日期
    private static final int QIXIRQ_NUM = 13;   // 起息日期
    private static final int DAOQRQ_NUM = 14;   // 到期日期
    private static final int QIXIAN_NUM = 15;   // 期限
    private static final int DAIKXT_NUM = 16;   // 贷款形态
    private static final int YNGJZT_NUM = 17;   // 应计非应计状态
    private static final int DKZHZT_NUM = 18;   // 贷款账户状态
    private static final int DBKKSX_NUM = 19;   // 多笔贷款扣款顺序
    private static final int HUOBDH_NUM = 20;   // 货币代号
    private static final int HTNGJE_NUM = 21;   // 合同金额
    private static final int JIEJJE_NUM = 22;   // 借据金额
    private static final int YFNGJE_NUM = 23;   // 已发放金额
    private static final int DJKFJE_NUM = 24;   // 冻结可放金额
    private static final int KFNGJE_NUM = 25;   // 可发放金额
    private static final int ZHCHBJ_NUM = 26;   // 正常本金
    private static final int YUQIBJ_NUM = 27;   // 逾期本金
    private static final int DZHIBJ_NUM = 28;   // 呆滞本金
    private static final int DAIZBJ_NUM = 29;   // 呆账本金
    private static final int DAIKJJ_NUM = 30;   // 贷款基金
    private static final int YSYJLX_NUM = 31;   // 应收应计利息
    private static final int CSYJLX_NUM = 32;   // 催收应计利息
    private static final int YINSQX_NUM = 33;   // 应收欠息
    private static final int CUISQX_NUM = 34;   // 催收欠息
    private static final int YSYJFX_NUM = 35;   // 应收应计罚息
    private static final int CSYJFX_NUM = 36;   // 催收应计罚息
    private static final int YINSFX_NUM = 37;   // 应收罚息
    private static final int CUISFX_NUM = 38;   // 催收罚息
    private static final int YJFUXI_NUM = 39;   // 应计复息
    private static final int FUXIII_NUM = 40;   // 复息
    private static final int YINJTX_NUM = 41;   // 应计贴息
    private static final int YINSTX_NUM = 42;   // 应收贴息
    private static final int DTLIXI_NUM = 43;   // 待摊利息
    private static final int HEXIBJ_NUM = 44;   // 核销本金
    private static final int HEXILX_NUM = 45;   // 核销利息
    private static final int ZHHABJ_NUM = 46;   // 置换本金
    private static final int ZHHALX_NUM = 47;   // 置换利息
    private static final int LIXISR_NUM = 48;   // 利息收入
    private static final int YINSFY_NUM = 49;   // 应收费用
    private static final int FEIYSR_NUM = 50;   // 费用收入
    private static final int YINSFJ_NUM = 51;   // 应收罚金
    private static final int FAJNSR_NUM = 52;   // 罚金收入
    private static final int ZHUNBJ_NUM = 53;   // 准备金
    private static final int ZHJYRQ_NUM = 54;   // 最后财务交易日
    private static final int ZHIXRQ_NUM = 55;   // 止息日期
    private static final int BEIZHU_NUM = 56;   // 备注
    private static final int MXXHAO_NUM = 57;   // 明细序号
    private static final int KAIHJG_NUM = 58;   // 开户机构
    private static final int KAIHGY_NUM = 59;   // 开户柜员
    private static final int WEIHRQ_NUM = 60;   // 维护日期
    private static final int WEIHGY_NUM = 61;   // 维护柜员
    private static final int XIOHRQ_NUM = 62;   // 销户日期
    private static final int XIOHGY_NUM = 63;   // 销户柜员
    private static final int WEIHJG_NUM = 64;   // 维护机构
    private static final int WEIHSJ_NUM = 65;   // 维护时间
    private static final int SHJNCH_NUM = 66;   // 时间戳
    private static final int JILUZT_NUM = 67;   // 记录状态

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 67;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbSCoreAdkzhDao lbSCoreAdkzhDao;
    @Value("${project.core.adkzh.filename:CBS_ADKZH_[DATE].dat}")
    private String fileName;
    @Value("${project.core.pprod.remoteFilePath:/hsdata/logs/dev/loan4-0-hnnx/file/}")
    //@Value("${project.core.adkzh.remoteFilePath:/nfsdata/file/midd/CORE/ADKZH/data/[DATE]/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbSCoreAdkzhData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbSCoreAdkzhData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst());

            // 使用并行流处理数据，提升性能
            List<LbSCoreAdkzhData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSCoreAdkzhData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbSCoreAdkzhData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "贷款账户数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "贷款账户数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_s_core_adkzh");
            lbSCoreAdkzhDao.deleteAll();
        }

        // 使用并行流进行数据转换和验证
        List<LbSCoreAdkzhDo> insertList = dataList.parallelStream().map(LbSCoreAdkzhConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条贷款账户数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.S_CORE_ADKZH_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 设置默认fixNum
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null，设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));

        log.info(prefixLog + "配置的远程文件路径: [{}]", remoteFilePathDefine);
        log.info(prefixLog + "转换后的本地文件路径: [{}]", localFilePath);
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在，尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在，请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 并行处理原始数据
     */
    private List<LbSCoreAdkzhData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbSCoreAdkzhData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据 - 根据LB_S_CORE_ADKZH表结构解析67个字段
     */
    private LbSCoreAdkzhData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbSCoreAdkzhData fileData = new LbSCoreAdkzhData();

        // 设置字符串字段
        fileData.setFaredm(split[FAREDM_NUM - 1]);
        fileData.setDaikzh(split[DAIKZH_NUM - 1]);
        fileData.setDkjeju(split[DKJEJU_NUM - 1]);
        fileData.setHtngbh(split[HTNGBH_NUM - 1]);
        fileData.setKehhao(split[KEHHAO_NUM - 1]);
        fileData.setKehzwm(split[KEHZWM_NUM - 1]);
        fileData.setYngyjg(split[YNGYJG_NUM - 1]);
        fileData.setZhngjg(split[ZHNGJG_NUM - 1]);
        fileData.setChapdm(split[CHAPDM_NUM - 1]);
        fileData.setChapmc(split[CHAPMC_NUM - 1]);
        fileData.setDkkjlb(split[DKKJLB_NUM - 1]);
        fileData.setKaihrq(split[KAIHRQ_NUM - 1]);
        fileData.setQixirq(split[QIXIRQ_NUM - 1]);
        fileData.setDaoqrq(split[DAOQRQ_NUM - 1]);
        fileData.setQixian(split[QIXIAN_NUM - 1]);
        fileData.setDaikxt(split[DAIKXT_NUM - 1]);
        fileData.setYngjzt(split[YNGJZT_NUM - 1]);
        fileData.setDkzhzt(split[DKZHZT_NUM - 1]);
        fileData.setHuobdh(split[HUOBDH_NUM - 1]);
        fileData.setZhjyrq(split[ZHJYRQ_NUM - 1]);
        fileData.setZhixrq(split[ZHIXRQ_NUM - 1]);
        fileData.setBeizhu(split[BEIZHU_NUM - 1]);
        fileData.setKaihjg(split[KAIHJG_NUM - 1]);
        fileData.setKaihgy(split[KAIHGY_NUM - 1]);
        fileData.setWeihrq(split[WEIHRQ_NUM - 1]);
        fileData.setWeihgy(split[WEIHGY_NUM - 1]);
        fileData.setXiohrq(split[XIOHRQ_NUM - 1]);
        fileData.setXiohgy(split[XIOHGY_NUM - 1]);
        fileData.setWeihjg(split[WEIHJG_NUM - 1]);
        fileData.setJiluzt(split[JILUZT_NUM - 1]);

        // 安全解析数字字段
        fileData.setDbkksx(parseBigDecimalSafely(split[DBKKSX_NUM - 1], parseErrorCount));
        fileData.setHtngje(parseBigDecimalSafely(split[HTNGJE_NUM - 1], parseErrorCount));
        fileData.setJiejje(parseBigDecimalSafely(split[JIEJJE_NUM - 1], parseErrorCount));
        fileData.setYfngje(parseBigDecimalSafely(split[YFNGJE_NUM - 1], parseErrorCount));
        fileData.setDjkfje(parseBigDecimalSafely(split[DJKFJE_NUM - 1], parseErrorCount));
        fileData.setKfngje(parseBigDecimalSafely(split[KFNGJE_NUM - 1], parseErrorCount));
        fileData.setZhchbj(parseBigDecimalSafely(split[ZHCHBJ_NUM - 1], parseErrorCount));
        fileData.setYuqibj(parseBigDecimalSafely(split[YUQIBJ_NUM - 1], parseErrorCount));
        fileData.setDzhibj(parseBigDecimalSafely(split[DZHIBJ_NUM - 1], parseErrorCount));
        fileData.setDaizbj(parseBigDecimalSafely(split[DAIZBJ_NUM - 1], parseErrorCount));
        fileData.setDaikjj(parseBigDecimalSafely(split[DAIKJJ_NUM - 1], parseErrorCount));
        fileData.setYsyjlx(parseBigDecimalSafely(split[YSYJLX_NUM - 1], parseErrorCount));
        fileData.setCsyjlx(parseBigDecimalSafely(split[CSYJLX_NUM - 1], parseErrorCount));
        fileData.setYinsqx(parseBigDecimalSafely(split[YINSQX_NUM - 1], parseErrorCount));
        fileData.setCuisqx(parseBigDecimalSafely(split[CUISQX_NUM - 1], parseErrorCount));
        fileData.setYsyjfx(parseBigDecimalSafely(split[YSYJFX_NUM - 1], parseErrorCount));
        fileData.setCsyjfx(parseBigDecimalSafely(split[CSYJFX_NUM - 1], parseErrorCount));
        fileData.setYinsfx(parseBigDecimalSafely(split[YINSFX_NUM - 1], parseErrorCount));
        fileData.setCuisfx(parseBigDecimalSafely(split[CUISFX_NUM - 1], parseErrorCount));
        fileData.setYjfuxi(parseBigDecimalSafely(split[YJFUXI_NUM - 1], parseErrorCount));
        fileData.setFuxiii(parseBigDecimalSafely(split[FUXIII_NUM - 1], parseErrorCount));
        fileData.setYinjtx(parseBigDecimalSafely(split[YINJTX_NUM - 1], parseErrorCount));
        fileData.setYinstx(parseBigDecimalSafely(split[YINSTX_NUM - 1], parseErrorCount));
        fileData.setDtlixi(parseBigDecimalSafely(split[DTLIXI_NUM - 1], parseErrorCount));
        fileData.setHexibj(parseBigDecimalSafely(split[HEXIBJ_NUM - 1], parseErrorCount));
        fileData.setHexilx(parseBigDecimalSafely(split[HEXILX_NUM - 1], parseErrorCount));
        fileData.setZhhabj(parseBigDecimalSafely(split[ZHHABJ_NUM - 1], parseErrorCount));
        fileData.setZhhalx(parseBigDecimalSafely(split[ZHHALX_NUM - 1], parseErrorCount));
        fileData.setLixisr(parseBigDecimalSafely(split[LIXISR_NUM - 1], parseErrorCount));
        fileData.setYinsfy(parseBigDecimalSafely(split[YINSFY_NUM - 1], parseErrorCount));
        fileData.setFeiysr(parseBigDecimalSafely(split[FEIYSR_NUM - 1], parseErrorCount));
        fileData.setYinsfj(parseBigDecimalSafely(split[YINSFJ_NUM - 1], parseErrorCount));
        fileData.setFajnsr(parseBigDecimalSafely(split[FAJNSR_NUM - 1], parseErrorCount));
        fileData.setZhunbj(parseBigDecimalSafely(split[ZHUNBJ_NUM - 1], parseErrorCount));
        fileData.setMxxhao(parseBigDecimalSafely(split[MXXHAO_NUM - 1], parseErrorCount));
        fileData.setWeihsj(parseBigDecimalSafely(split[WEIHSJ_NUM - 1], parseErrorCount));
        fileData.setShjnch(parseBigDecimalSafely(split[SHJNCH_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 数据验证方法
     */
    private boolean validateData(LbSCoreAdkzhDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getFaredm())) {
            log.warn("法人代码为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getDaikzh())) {
            log.warn("贷款账号为空,法人代码:[{}]", data.getFaredm());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     */
    private void processBatchInsert(List<LbSCoreAdkzhDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbSCoreAdkzhDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbSCoreAdkzhDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }
}
